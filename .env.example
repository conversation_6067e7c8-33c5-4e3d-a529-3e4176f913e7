# Discord Bot Configuration
# Copy this file to .env and fill in your actual values

# Discord Bot Token (Required)
# Get this from Discord Developer Portal > Bot > Token
DISCORD_TOKEN=MTM5ODA0MTA2ODcwMzk3NzYxMw.G4fnfA._bxV9ObQxkSzDBXld2xDTqPZwN_Whi6R2l3jLk

# Discord Application ID (Required)
# Get this from Discord Developer Portal > General Information > Application ID
APPLICATION_ID=1398041068703977613

# OAuth2 Configuration (Required for auto-join features)
# Client Secret from Discord Developer Portal > OAuth2 > Client Secret
DISCORD_CLIENT_SECRET=4TZkq1G66WyG-_X1ykVi5i0J7u9IWhch

# Redirect URI for OAuth2 callback (Required for auto-join features)
# This should match what you set in Discord Developer Portal > OAuth2 > Redirects
# For local development, use: http://localhost:8080/callback
REDIRECT_URI=http://localhost:8080/callback

# Optional: Custom OAuth2 callback port (default: 8080)
# OAUTH_PORT=8080

# Database Configuration (Optional - uses SQLite by default)
# DATABASE_URL=sqlite:///bot_data.db

# Logging Configuration (Optional)
# LOG_LEVEL=INFO
# LOG_FILE=bot.log