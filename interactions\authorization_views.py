"""
Authorization-related views and interactive components
"""
import discord
from interactions.views.base import BaseView
from embeds.builders import create_success_embed, create_error_embed, InfoEmbed
from utils.database import db_manager
from core.logging import logger
from config.messages import (
    AuthorizationConfig, ButtonLabels, EmbedTitles,
    get_authorization_embed_description, get_confirmation_embed_description,
    ContactMessages, SystemConfig
)

class QuickAuthorizationView(BaseView):
    """View with quick authorization button for giveaway warnings"""

    def __init__(self, user_id: int):
        super().__init__(timeout=SystemConfig.AUTHORIZATION_TIMEOUT, user_id=user_id)

    @discord.ui.button(
        label=ButtonLabels.START_AUTHORIZATION,
        style=discord.ButtonStyle.primary,
        emoji="🔐"
    )
    async def quick_authorize(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Handle quick authorization button click"""
        try:
            # Check if user is already authorized
            if await db_manager.is_user_authorized(interaction.user.id):
                embed = create_success_embed(
                    EmbedTitles.ALREADY_AUTHORIZED,
                    "You're already authorized! You can now participate in giveaways."
                )
                await interaction.response.edit_message(embed=embed, view=None)
                return

            # Create OAuth URL using configuration
            auth_url = AuthorizationConfig.get_oauth_url()

            # Create authorization embed using configuration
            description = (
                f"**Complete your authorization to participate in giveaways:**\n\n"
                f"**Step 1:** [**🚀 Click here to authorize**]({auth_url})\n"
                f"**Step 2:** Use `/confirm_auth` command after authorization\n\n"
                f"{get_authorization_embed_description(include_steps=False)}"
            )

            embed = InfoEmbed(EmbedTitles.QUICK_AUTH_SETUP, description).build()

            # Add confirm authorization button
            view = ConfirmAuthorizationView(interaction.user.id)
            await interaction.response.edit_message(embed=embed, view=view)
            
            logger.debug(f"Quick authorization started for {interaction.user}")
            
        except Exception as e:
            logger.error(f"Error in quick authorization: {e}")
            embed = create_error_embed(
                EmbedTitles.AUTH_FAILED,
                "Something went wrong. Please try using the `/authorize` command manually."
            )
            await interaction.response.edit_message(embed=embed, view=None)

class ConfirmAuthorizationView(BaseView):
    """View for confirming authorization after OAuth"""

    def __init__(self, user_id: int):
        super().__init__(timeout=SystemConfig.CONFIRMATION_TIMEOUT, user_id=user_id)

    @discord.ui.button(
        label=ButtonLabels.CONFIRM_AUTHORIZATION,
        style=discord.ButtonStyle.success,
        emoji="✅"
    )
    async def confirm_auth(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Handle authorization confirmation with OAuth verification"""
        try:
            # Check if already authorized
            if await db_manager.is_user_authorized(interaction.user.id):
                embed = create_success_embed(
                    EmbedTitles.ALREADY_AUTHORIZED,
                    "Your authorization is already confirmed! You can now participate in giveaways."
                )
                await interaction.response.edit_message(embed=embed, view=None)
                return

            # Verify OAuth authorization first
            from core.bot import bot
            is_verified = await AuthorizationConfig.verify_oauth_authorization(bot, interaction.user.id)

            if not is_verified:
                embed = create_error_embed(
                    EmbedTitles.AUTH_FAILED,
                    "❌ **Authorization verification failed!**\n\n"
                    "It looks like you haven't completed the OAuth authorization yet.\n\n"
                    "**Please:**\n"
                    "1️⃣ Click the authorization link above\n"
                    "2️⃣ Complete the Discord authorization process\n"
                    "3️⃣ Then click this button again\n\n"
                    "**Note:** You must actually authorize the bot, not just click this button!"
                )
                await interaction.response.edit_message(embed=embed, view=self)
                logger.warning(f"OAuth verification failed for {interaction.user}")
                return

            # Add user to authorized list (only after verification)
            success = await db_manager.add_authorized_user(interaction.user.id, str(interaction.user))

            if success:
                embed = create_success_embed(
                    EmbedTitles.AUTH_CONFIRMED,
                    get_confirmation_embed_description()
                )
                await interaction.response.edit_message(embed=embed, view=None)
                logger.info(f"User authorized via quick auth (verified): {interaction.user}")
            else:
                embed = create_error_embed(
                    EmbedTitles.AUTH_FAILED,
                    "There was an error confirming your authorization. Please try again or contact server administrators."
                )
                await interaction.response.edit_message(embed=embed, view=None)

        except Exception as e:
            logger.error(f"Error confirming authorization: {e}")
            embed = create_error_embed(
                EmbedTitles.AUTH_FAILED,
                "Something went wrong. Please try using the `/confirm_auth` command manually."
            )
            await interaction.response.edit_message(embed=embed, view=None)

    @discord.ui.button(
        label=ButtonLabels.CANCEL_AUTHORIZATION,
        style=discord.ButtonStyle.secondary,
        emoji="❌"
    )
    async def cancel_auth(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Cancel authorization process"""
        embed = create_error_embed(
            EmbedTitles.AUTH_CANCELLED,
            "Authorization process cancelled. You can try again anytime by clicking the giveaway button."
        )
        await interaction.response.edit_message(embed=embed, view=None)

class GiveawayWarningView(BaseView):
    """Warning view for giveaway participation issues"""

    def __init__(self, user_id: int, warning_type: str = "authorization"):
        super().__init__(timeout=SystemConfig.AUTHORIZATION_TIMEOUT, user_id=user_id)
        self.warning_type = warning_type

    @discord.ui.button(
        label=ButtonLabels.QUICK_AUTHORIZATION,
        style=discord.ButtonStyle.primary,
        emoji="🔐"
    )
    async def start_authorization(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Start quick authorization process"""
        try:
            # Check if user is already authorized
            if await db_manager.is_user_authorized(interaction.user.id):
                embed = create_success_embed(
                    EmbedTitles.ALREADY_AUTHORIZED,
                    "You're already authorized! You can now participate in giveaways."
                )
                await interaction.response.edit_message(embed=embed, view=None)
                return

            # Create OAuth URL using configuration
            auth_url = AuthorizationConfig.get_oauth_url()

            # Create authorization embed using configuration
            description = (
                f"**Complete your authorization to participate in giveaways:**\n\n"
                f"**Step 1:** [**🚀 Click here to authorize**]({auth_url})\n"
                f"**Step 2:** Use `/confirm_auth` command after authorization\n\n"
                f"{get_authorization_embed_description(include_steps=False)}"
            )

            embed = InfoEmbed(EmbedTitles.QUICK_AUTH_SETUP, description).build()

            # Add confirm authorization button
            view = ConfirmAuthorizationView(interaction.user.id)
            await interaction.response.edit_message(embed=embed, view=view)

            logger.debug(f"Quick authorization started for {interaction.user}")

        except Exception as e:
            logger.error(f"Error in quick authorization: {e}")
            embed = create_error_embed(
                EmbedTitles.AUTH_FAILED,
                "Something went wrong. Please try using the `/authorize` command manually."
            )
            await interaction.response.edit_message(embed=embed, view=None)
    
    @discord.ui.button(
        label=ButtonLabels.CONTACT_ADMINS,
        style=discord.ButtonStyle.secondary,
        emoji="📞"
    )
    async def contact_admins(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Provide information on how to contact server administrators"""
        contact_info = await ContactMessages.get_contact_info(interaction.guild)

        embed = InfoEmbed(
            EmbedTitles.CONTACT_ADMINS,
            contact_info
        ).build()

        await interaction.response.edit_message(embed=embed, view=None)
