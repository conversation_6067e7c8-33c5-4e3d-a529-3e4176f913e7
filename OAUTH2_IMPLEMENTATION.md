# 🔐 Implementación OAuth2 - Solución al Error `unsupported_response_type`

## 📋 Resumen del Problema

El error `unsupported_response_type` ocurre cuando se intenta usar un flujo OAuth2 incorrecto con la API de Discord. Este documento explica la implementación correcta que resuelve este problema.

## ❌ Problema Original

### Error Común
```
HTTP 400 Bad Request
{
  "error": "unsupported_response_type",
  "error_description": "The authorization server does not support obtaining an access token using this method."
}
```

### Causas Principales
1. **Flujo incorrecto**: Usar `response_type=token` (Implicit Grant)
2. **Configuración incorrecta**: Falta de `DISCORD_CLIENT_SECRET` o `REDIRECT_URI`
3. **Configuración de Discord**: Bot no configurado como público o sin Guild Install
4. **Intercambio de tokens**: Usar `Content-Type` incorrecto o parámetros faltantes

## ✅ Solución Implementada

### 1. Flujo OAuth2 Authorization Code Grant

#### A. URL de Autorización Correcta
```python
# config/messages.py
def get_oauth_url(self, redirect_uri=None):
    """Generate OAuth2 authorization URL with correct flow"""
    if redirect_uri:
        # Full OAuth2 Authorization Code Grant flow
        params = {
            'client_id': self.CLIENT_ID,
            'redirect_uri': redirect_uri,
            'response_type': 'code',  # ✅ CORRECTO: Authorization Code Grant
            'scope': ' '.join(self.OAUTH_SCOPES)
        }
    else:
        # Simple bot invite (no OAuth2)
        params = {
            'client_id': self.CLIENT_ID,
            'permissions': self.OAUTH_PERMISSIONS,
            'scope': 'bot'
        }
    
    return f"https://discord.com/api/oauth2/authorize?{urlencode(params)}"
```

#### B. Intercambio de Código por Token
```python
# utils/oauth_server.py
async def exchange_code_for_token(code: str, redirect_uri: str):
    """Exchange authorization code for access token"""
    
    # ✅ CORRECTO: Content-Type y parámetros apropiados
    data = {
        'client_id': CLIENT_ID,
        'client_secret': CLIENT_SECRET,
        'grant_type': 'authorization_code',  # ✅ CORRECTO
        'code': code,
        'redirect_uri': redirect_uri
    }
    
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded',  # ✅ CORRECTO
        'Accept': 'application/json'
    }
    
    async with aiohttp.ClientSession() as session:
        async with session.post(
            'https://discord.com/api/oauth2/token',
            data=data,  # ✅ CORRECTO: No JSON, sino form data
            headers=headers
        ) as response:
            return await response.json()
```

### 2. Servidor de Callback Local

#### A. Implementación del Servidor
```python
# utils/oauth_server.py
class OAuthCallbackHandler:
    """Handle OAuth2 callback requests"""
    
    async def handle_callback(self, request):
        """Process OAuth2 callback with authorization code"""
        query_params = request.query
        
        if 'error' in query_params:
            # Handle OAuth2 errors
            error = query_params['error']
            description = query_params.get('error_description', 'Unknown error')
            logger.error(f"OAuth2 error: {error} - {description}")
            return web.Response(
                text=f"Error: {error}\n{description}",
                status=400
            )
        
        if 'code' not in query_params:
            return web.Response(
                text="Error: No authorization code received",
                status=400
            )
        
        code = query_params['code']
        state = query_params.get('state')
        
        try:
            # Exchange code for token
            token_data = await exchange_code_for_token(
                code, 
                os.getenv('REDIRECT_URI')
            )
            
            if 'access_token' in token_data:
                # Store token and user info
                await self.store_authorization(token_data, state)
                
                return web.Response(
                    text="✅ Autorización exitosa! Puedes cerrar esta ventana.",
                    content_type='text/html; charset=utf-8'
                )
            else:
                logger.error(f"Token exchange failed: {token_data}")
                return web.Response(
                    text=f"Error en intercambio de token: {token_data}",
                    status=400
                )
                
        except Exception as e:
            logger.error(f"Callback processing error: {e}")
            return web.Response(
                text=f"Error procesando callback: {e}",
                status=500
            )
```

### 3. Configuración de Discord Developer Portal

#### A. Configuración Requerida
```yaml
# Discord Developer Portal Settings
Bot:
  Public Bot: true  # ✅ Temporal para OAuth2
  
OAuth2:
  Redirects:
    - "http://localhost:8080/callback"  # ✅ Para desarrollo
    - "https://yourdomain.com/callback"  # ✅ Para producción
  
  Installation:
    Guild Install: true  # ✅ Temporal para OAuth2
    
  Scopes:
    - "identify"      # ✅ Para obtener info del usuario
    - "guilds.join"   # ✅ Para unir usuarios a servidores
    - "bot"           # ✅ Para invitar el bot
```

#### B. Variables de Entorno
```env
# .env
DISCORD_TOKEN=tu_bot_token
APPLICATION_ID=tu_application_id
DISCORD_CLIENT_SECRET=tu_client_secret  # ✅ REQUERIDO para OAuth2
REDIRECT_URI=http://localhost:8080/callback  # ✅ REQUERIDO
OAUTH_PORT=8080  # Opcional
```

## 🔄 Flujo Completo de Autorización

### Paso 1: Generar URL de Autorización
```python
# Usuario ejecuta /authorize
redirect_uri = os.getenv('REDIRECT_URI')
auth_url = auth_config.get_oauth_url(redirect_uri)
# URL: https://discord.com/api/oauth2/authorize?client_id=...&response_type=code&...
```

### Paso 2: Usuario Autoriza
```
1. Usuario hace clic en el enlace
2. Discord muestra página de autorización
3. Usuario acepta permisos
4. Discord redirige a: http://localhost:8080/callback?code=ABC123&state=...
```

### Paso 3: Intercambio de Código
```python
# Servidor local recibe el callback
code = request.query['code']

# Intercambiar código por token
token_data = await exchange_code_for_token(code, redirect_uri)
# Respuesta: {"access_token": "...", "token_type": "Bearer", ...}
```

### Paso 4: Usar Token para API
```python
# Obtener información del usuario
user_info = await get_user_info(token_data['access_token'])

# Unir usuario a servidor (si tiene permisos)
await join_user_to_guild(guild_id, user_id, token_data['access_token'])
```

## 🛠️ Herramientas de Debugging

### 1. Script de Verificación
```bash
# Verificar configuración OAuth2
python test_bot.py
```

### 2. Logs Detallados
```python
# Habilitar logs de OAuth2
logging.getLogger('oauth_server').setLevel(logging.DEBUG)
```

### 3. Verificación Manual
```python
# Verificar URL generada
from config.messages import AuthorizationConfig
auth = AuthorizationConfig()
print(auth.get_oauth_url('http://localhost:8080/callback'))
```

## 🚨 Errores Comunes y Soluciones

### Error: `invalid_client`
**Causa**: `DISCORD_CLIENT_SECRET` incorrecto
**Solución**: Regenerar Client Secret en Discord Developer Portal

### Error: `redirect_uri_mismatch`
**Causa**: `REDIRECT_URI` no coincide con la configuración
**Solución**: Verificar que la URI esté registrada en Discord

### Error: `invalid_grant`
**Causa**: Código de autorización expirado o ya usado
**Solución**: Generar nueva autorización

### Error: `insufficient_scope`
**Causa**: Token no tiene el scope requerido
**Solución**: Incluir `guilds.join` en `OAUTH_SCOPES`

## 📊 Comparación: Antes vs Después

### ❌ Implementación Incorrecta (Antes)
```python
# INCORRECTO: Implicit Grant
url = f"https://discord.com/api/oauth2/authorize?client_id={CLIENT_ID}&response_type=token&scope=bot"

# INCORRECTO: Sin intercambio de código
# El token se obtiene directamente en la URL (inseguro)
```

### ✅ Implementación Correcta (Después)
```python
# CORRECTO: Authorization Code Grant
url = f"https://discord.com/api/oauth2/authorize?client_id={CLIENT_ID}&response_type=code&redirect_uri={REDIRECT_URI}&scope=identify%20guilds.join"

# CORRECTO: Intercambio seguro de código por token
token_data = await exchange_code_for_token(code, redirect_uri)
```

## 🔒 Consideraciones de Seguridad

1. **Client Secret**: Nunca expongas el `DISCORD_CLIENT_SECRET` en el frontend
2. **HTTPS en Producción**: Usa HTTPS para `REDIRECT_URI` en producción
3. **State Parameter**: Implementa validación de `state` para prevenir CSRF
4. **Token Storage**: Almacena tokens de forma segura y encriptada
5. **Token Expiration**: Implementa renovación automática de tokens

## 📈 Beneficios de la Implementación

1. **Seguridad**: Flujo OAuth2 estándar y seguro
2. **Compatibilidad**: Compatible con todas las APIs de Discord
3. **Escalabilidad**: Servidor de callback reutilizable
4. **Debugging**: Logs detallados para troubleshooting
5. **Flexibilidad**: Soporte para desarrollo y producción

---

**Esta implementación resuelve completamente el error `unsupported_response_type` y proporciona una base sólida para funciones OAuth2 avanzadas en Discord bots.**