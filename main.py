"""
Memory-optimized Discord bot main application.
Implements discord.py v2.0+ features with 1GB RAM constraint optimization.
"""
import asyncio
import signal
import sys
import os
from typing import Optional

# Load environment variables FIRST (CRITICAL FIX)
from dotenv import load_dotenv
load_dotenv()

from core.bot import bot
from core.config import config
from core.logging import logger
from core.exceptions import BotException
from utils.database import db_manager

class BotApplication:
    """Main bot application with proper lifecycle management"""
    
    def __init__(self):
        self.bot = bot
        self.db_manager = db_manager
        self._shutdown_event = asyncio.Event()
    
    async def setup(self):
        """Setup bot components"""
        try:
            logger.info("Setting up bot application...")
            
            # Validate critical environment variables (CRITICAL FIX)
            self._validate_environment()
            
            # Initialize database
            await self.db_manager.initialize_database()
            
            # Load extensions/cogs here if needed
            await self._load_extensions()
            
            logger.info("Bot application setup completed")
            
        except Exception as e:
            logger.error(f"Failed to setup bot application: {e}")
            raise BotException(f"Setup failed: {e}")
    
    def _validate_environment(self):
        """Validate that all required environment variables are loaded"""
        required_vars = {
            'DISCORD_TOKEN': 'Discord bot token',
            'DISCORD_CLIENT_SECRET': 'Discord OAuth2 client secret'
        }
        
        missing_vars = []
        placeholder_vars = []
        
        for var_name, description in required_vars.items():
            value = os.getenv(var_name)
            if not value:
                missing_vars.append(f"{var_name} ({description})")
            elif 'your_' in value.lower() or 'here' in value.lower() or 'placeholder' in value.lower():
                placeholder_vars.append(f"{var_name} (appears to be placeholder)")
        
        if missing_vars or placeholder_vars:
            logger.error("❌ Environment variable validation failed!")
            if missing_vars:
                logger.error(f"Missing variables: {', '.join(missing_vars)}")
            if placeholder_vars:
                logger.error(f"Placeholder variables: {', '.join(placeholder_vars)}")
            logger.error("📋 Please check your .env file and restart the bot")
            return False
        
        # Log successful validation
        logger.info("✅ Environment variables validated successfully")
        logger.info(f"  DISCORD_TOKEN: {'✅ Set' if os.getenv('DISCORD_TOKEN') else '❌ Missing'}")
        logger.info(f"  DISCORD_CLIENT_SECRET: {'✅ Set' if os.getenv('DISCORD_CLIENT_SECRET') else '❌ Missing'}")
        logger.info(f"  REDIRECT_URI: {os.getenv('REDIRECT_URI', 'Using default: http://localhost:8080/callback')}")
        
        return True
    
    async def _load_extensions(self):
        """Load bot extensions/cogs"""
        # For now, we'll add commands directly to the bot
        # In a larger application, you would load cogs here
        await self._setup_commands()
    
    async def _setup_commands(self):
        """Setup bot commands"""
        try:
            from commands.basic import setup_basic_commands
            from commands.giveaway import setup_giveaway_commands
            from commands.admin import setup_admin_commands
            from commands.slash_commands import setup_slash_commands
            from events.member_events import setup_member_events

            # Setup command groups
            setup_basic_commands(self.bot)
            setup_giveaway_commands(self.bot)
            setup_admin_commands(self.bot)
            setup_slash_commands(self.bot)

            # Setup event handlers
            setup_member_events(self.bot)

            logger.info("All commands loaded successfully")
        except ImportError as e:
            logger.error(f"Failed to import commands: {e}")
            raise BotException(f"Command loading failed: {e}")
        except Exception as e:
            logger.error(f"Failed to sync slash commands: {e}")
            # Don't fail startup if slash command sync fails
            logger.warning("Continuing without slash command sync")
    
    async def start(self):
        """Start the bot"""
        try:
            logger.info("Starting Discord bot...")
            
            # Setup signal handlers for graceful shutdown
            self._setup_signal_handlers()
            
            # Setup bot components
            await self.setup()
            
            # Start bot
            logger.info(f"Attempting to connect with token: {config.token[:20]}...")
            await self.bot.start(config.token)
            
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt, shutting down...")
        except Exception as e:
            logger.error(f"Bot startup failed: {e}")
            raise
        finally:
            await self.shutdown()
    
    async def shutdown(self):
        """Graceful shutdown"""
        try:
            logger.info("Shutting down bot application...")
            
            # Close bot connection
            if not self.bot.is_closed():
                await self.bot.close()
            
            # Set shutdown event
            self._shutdown_event.set()
            
            logger.info("Bot application shutdown completed")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
    
    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating shutdown...")
            asyncio.create_task(self.shutdown())
        
        # Only setup signal handlers on Unix systems
        if sys.platform != 'win32':
            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)

async def main():
    """Main entry point"""
    app = BotApplication()
    
    try:
        await app.start()
    except Exception as e:
        logger.error(f"Application failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    # Run the bot
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)
