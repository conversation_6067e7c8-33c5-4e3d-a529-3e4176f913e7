"""
Memory-efficient database utilities for Discord bot.
Implements connection pooling and efficient query patterns.
"""
import aiosqlite
import asyncio
from typing import Optional, List, Dict, Any, AsyncContextManager
from contextlib import asynccontextmanager
import json
from datetime import datetime

from core.config import config
from core.logging import logger
from core.exceptions import DatabaseError

class DatabaseManager:
    """
    Memory-efficient database manager with connection pooling.
    
    Features:
    - Automatic connection management
    - Query result caching for frequently accessed data
    - Efficient batch operations
    - Proper resource cleanup
    """
    
    def __init__(self, database_url: str = None):
        self.database_url = database_url or config.database_url
        self._connection_semaphore = asyncio.Semaphore(config.database_pool_size)
        self._initialized = False
    
    @asynccontextmanager
    async def get_connection(self) -> AsyncContextManager[aiosqlite.Connection]:
        """Get database connection with automatic cleanup"""
        async with self._connection_semaphore:
            try:
                async with aiosqlite.connect(self.database_url) as conn:
                    # Enable foreign keys and WAL mode for better performance
                    await conn.execute("PRAGMA foreign_keys = ON")
                    await conn.execute("PRAGMA journal_mode = WAL")
                    yield conn
            except Exception as e:
                logger.error(f"Database connection error: {e}")
                raise DatabaseError(f"Failed to connect to database: {e}")
    
    async def initialize_database(self):
        """Initialize database tables"""
        if self._initialized:
            return

        async with self.get_connection() as conn:
            # Authorized users table
            await conn.execute('''
                CREATE TABLE IF NOT EXISTS authorized_users (
                    user_id INTEGER PRIMARY KEY,
                    username TEXT NOT NULL,
                    authorized_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    access_token TEXT DEFAULT NULL,
                    refresh_token TEXT DEFAULT NULL,
                    token_expires_at TIMESTAMP DEFAULT NULL,
                    last_token_test TIMESTAMP DEFAULT NULL
                )
            ''')

            # Active giveaways table
            await conn.execute('''
                CREATE TABLE IF NOT EXISTS active_giveaways (
                    message_id INTEGER PRIMARY KEY,
                    guild_id INTEGER NOT NULL,
                    channel_id INTEGER NOT NULL,
                    title TEXT NOT NULL,
                    description TEXT,
                    end_time TIMESTAMP NOT NULL,
                    winner_count INTEGER DEFAULT 1,
                    created_by INTEGER NOT NULL,
                    participants TEXT DEFAULT '[]',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Guild configuration table
            await conn.execute('''
                CREATE TABLE IF NOT EXISTS guild_config (
                    guild_id INTEGER PRIMARY KEY,
                    welcome_channel INTEGER,
                    activity_channel INTEGER,
                    activity_message_count INTEGER DEFAULT 50,
                    milestone_channel INTEGER,
                    milestone_count INTEGER DEFAULT 500,
                    last_member_count INTEGER DEFAULT 0,
                    nitro_emoji TEXT DEFAULT '💎',
                    robux_emoji TEXT DEFAULT '🔶',
                    garden_emoji TEXT DEFAULT '🌳',
                    custom_emoji TEXT DEFAULT '🎁',
                    giveaway_participant_roles TEXT DEFAULT '[]',
                    bot_admin_roles TEXT DEFAULT '[]',
                    require_authorization INTEGER DEFAULT 1,
                    role_bonus_multipliers TEXT DEFAULT '{}',
                    partner_servers TEXT DEFAULT '[]',
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Migrate existing guild_config table to add emoji columns if they don't exist
            await self._migrate_guild_config_table(conn)
            
            # Migrate existing authorized_users table to add OAuth token columns if they don't exist
            await self._migrate_authorized_users_table(conn)

            # Create indexes for better performance
            await conn.execute('CREATE INDEX IF NOT EXISTS idx_giveaways_end_time ON active_giveaways(end_time)')
            await conn.execute('CREATE INDEX IF NOT EXISTS idx_giveaways_guild ON active_giveaways(guild_id)')

            await conn.commit()

        self._initialized = True
        logger.info("Database initialized successfully")

    async def _migrate_guild_config_table(self, conn):
        """Migrate guild_config table to add new columns if they don't exist"""
        try:
            # Check if columns exist
            cursor = await conn.execute("PRAGMA table_info(guild_config)")
            columns = await cursor.fetchall()
            column_names = [col[1] for col in columns]

            # Add missing columns
            new_columns = [
                ('nitro_emoji', "TEXT DEFAULT '💎'"),
                ('robux_emoji', "TEXT DEFAULT '🔶'"),
                ('garden_emoji', "TEXT DEFAULT '🌳'"),
                ('custom_emoji', "TEXT DEFAULT '🎁'"),
                ('giveaway_participant_roles', "TEXT DEFAULT '[]'"),
                ('bot_admin_roles', "TEXT DEFAULT '[]'"),
                ('require_authorization', "INTEGER DEFAULT 1"),
                ('role_bonus_multipliers', "TEXT DEFAULT '{}'"),
                ('partner_servers', "TEXT DEFAULT '[]'"),
                ('contact_message', "TEXT DEFAULT NULL"),
                ('contact_discord_server', "TEXT DEFAULT NULL"),
                ('contact_website', "TEXT DEFAULT NULL"),
                ('contact_email', "TEXT DEFAULT NULL"),
                ('contact_methods', "TEXT DEFAULT '[]'"),
                ('auto_join_servers', "TEXT DEFAULT '{}'"),
                ('updated_at', "TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
            ]

            for column_name, column_def in new_columns:
                if column_name not in column_names:
                    await conn.execute(f'ALTER TABLE guild_config ADD COLUMN {column_name} {column_def}')
                    logger.info(f"Added column {column_name} to guild_config table")

        except Exception as e:
            logger.error(f"Error migrating guild_config table: {e}")
            # If migration fails, the table might not exist yet, which is fine
    
    async def _migrate_authorized_users_table(self, conn):
        """Migrate authorized_users table to add OAuth token columns if they don't exist"""
        try:
            # Check if columns exist
            cursor = await conn.execute("PRAGMA table_info(authorized_users)")
            columns = await cursor.fetchall()
            column_names = [col[1] for col in columns]

            # Add missing OAuth token columns
            new_columns = [
                ('access_token', "TEXT DEFAULT NULL"),
                ('refresh_token', "TEXT DEFAULT NULL"),
                ('token_expires_at', "TIMESTAMP DEFAULT NULL"),
                ('last_token_test', "TIMESTAMP DEFAULT NULL")
            ]

            for column_name, column_def in new_columns:
                if column_name not in column_names:
                    await conn.execute(f'ALTER TABLE authorized_users ADD COLUMN {column_name} {column_def}')
                    logger.info(f"Added column {column_name} to authorized_users table")

        except Exception as e:
            logger.error(f"Error migrating authorized_users table: {e}")
            # If migration fails, the table might not exist yet, which is fine
    
    # Authorized users methods
    async def add_authorized_user(self, user_id: int, username: str, access_token: str = None, refresh_token: str = None, expires_in: int = None) -> bool:
        """Add or update authorized user with optional OAuth tokens"""
        try:
            async with self.get_connection() as conn:
                # Calculate expiry time if expires_in is provided
                token_expires_at = None
                if expires_in:
                    from datetime import datetime, timedelta
                    token_expires_at = datetime.now() + timedelta(seconds=expires_in)
                
                await conn.execute('''
                    INSERT OR REPLACE INTO authorized_users 
                    (user_id, username, access_token, refresh_token, token_expires_at) 
                    VALUES (?, ?, ?, ?, ?)
                ''', (user_id, username, access_token, refresh_token, token_expires_at))
                await conn.commit()
                logger.debug(f"Added authorized user: {username} ({user_id}) with tokens: {bool(access_token)}")
                return True
        except Exception as e:
            logger.error(f"Error adding authorized user: {e}")
            return False
    
    async def is_user_authorized(self, user_id: int) -> bool:
        """Check if user is authorized"""
        try:
            async with self.get_connection() as conn:
                cursor = await conn.execute(
                    'SELECT 1 FROM authorized_users WHERE user_id = ?',
                    (user_id,)
                )
                result = await cursor.fetchone()
                return result is not None
        except Exception as e:
            logger.error(f"Error checking user authorization: {e}")
            return False
    
    async def get_authorized_users_count(self) -> int:
        """Get count of authorized users"""
        try:
            async with self.get_connection() as conn:
                cursor = await conn.execute('SELECT COUNT(*) FROM authorized_users')
                result = await cursor.fetchone()
                return result[0] if result else 0
        except Exception as e:
            logger.error(f"Error getting authorized users count: {e}")
            return 0
    
    async def get_authorized_users(self, limit: Optional[int] = None) -> List[tuple]:
        """Get list of authorized users"""
        try:
            async with self.get_connection() as conn:
                if limit:
                    cursor = await conn.execute(
                        'SELECT user_id, username FROM authorized_users ORDER BY authorized_at DESC LIMIT ?',
                        (limit,)
                    )
                else:
                    cursor = await conn.execute(
                        'SELECT user_id, username FROM authorized_users ORDER BY authorized_at DESC'
                    )
                return await cursor.fetchall()
        except Exception as e:
            logger.error(f"Error getting authorized users: {e}")
            return []
    
    async def get_user_oauth_tokens(self, user_id: int) -> dict:
        """Get OAuth tokens for a user"""
        try:
            async with self.get_connection() as conn:
                cursor = await conn.execute(
                    'SELECT access_token, refresh_token, token_expires_at, last_token_test FROM authorized_users WHERE user_id = ?',
                    (user_id,)
                )
                result = await cursor.fetchone()
                if result:
                    return {
                        'access_token': result[0],
                        'refresh_token': result[1],
                        'token_expires_at': result[2],
                        'last_token_test': result[3]
                    }
                return {}
        except Exception as e:
            logger.error(f"Error getting OAuth tokens for user {user_id}: {e}")
            return {}
    
    async def update_user_oauth_tokens(self, user_id: int, access_token: str = None, refresh_token: str = None, expires_in: int = None) -> bool:
        """Update OAuth tokens for an existing user"""
        try:
            async with self.get_connection() as conn:
                # Calculate expiry time if expires_in is provided
                token_expires_at = None
                if expires_in:
                    from datetime import datetime, timedelta
                    token_expires_at = datetime.now() + timedelta(seconds=expires_in)
                
                # Only update the provided fields
                if access_token and refresh_token:
                    await conn.execute('''
                        UPDATE authorized_users 
                        SET access_token = ?, refresh_token = ?, token_expires_at = ? 
                        WHERE user_id = ?
                    ''', (access_token, refresh_token, token_expires_at, user_id))
                elif access_token:
                    await conn.execute('''
                        UPDATE authorized_users 
                        SET access_token = ?, token_expires_at = ? 
                        WHERE user_id = ?
                    ''', (access_token, token_expires_at, user_id))
                
                await conn.commit()
                logger.debug(f"Updated OAuth tokens for user {user_id}")
                return True
        except Exception as e:
            logger.error(f"Error updating OAuth tokens for user {user_id}: {e}")
            return False
    
    async def mark_token_test(self, user_id: int, is_valid: bool) -> bool:
        """Mark when a token was last tested and if it was valid"""
        try:
            async with self.get_connection() as conn:
                from datetime import datetime
                
                # If token is invalid, clear the tokens
                if not is_valid:
                    await conn.execute('''
                        UPDATE authorized_users 
                        SET access_token = NULL, refresh_token = NULL, token_expires_at = NULL, last_token_test = ? 
                        WHERE user_id = ?
                    ''', (datetime.now(), user_id))
                else:
                    await conn.execute('''
                        UPDATE authorized_users 
                        SET last_token_test = ? 
                        WHERE user_id = ?
                    ''', (datetime.now(), user_id))
                
                await conn.commit()
                logger.debug(f"Marked token test for user {user_id}: {'valid' if is_valid else 'invalid'}")
                return True
        except Exception as e:
            logger.error(f"Error marking token test for user {user_id}: {e}")
            return False
    
    # Giveaway methods
    async def create_giveaway(self, message_id: int, guild_id: int, channel_id: int,
                            title: str, description: str, end_time: datetime,
                            winner_count: int, created_by: int) -> bool:
        """Create a new giveaway"""
        try:
            async with self.get_connection() as conn:
                await conn.execute('''
                    INSERT INTO active_giveaways 
                    (message_id, guild_id, channel_id, title, description, end_time, winner_count, created_by)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (message_id, guild_id, channel_id, title, description, end_time, winner_count, created_by))
                await conn.commit()
                logger.debug(f"Created giveaway: {title} ({message_id})")
                return True
        except Exception as e:
            logger.error(f"Error creating giveaway: {e}")
            return False
    
    async def get_giveaway(self, message_id: int) -> Optional[Dict[str, Any]]:
        """Get giveaway by message ID"""
        try:
            async with self.get_connection() as conn:
                cursor = await conn.execute(
                    'SELECT * FROM active_giveaways WHERE message_id = ?',
                    (message_id,)
                )
                result = await cursor.fetchone()
                
                if result:
                    return {
                        'message_id': result[0],
                        'guild_id': result[1],
                        'channel_id': result[2],
                        'title': result[3],
                        'description': result[4],
                        'end_time': result[5],
                        'winner_count': result[6],
                        'created_by': result[7],
                        'participants': json.loads(result[8]) if result[8] else []
                    }
                return None
        except Exception as e:
            logger.error(f"Error getting giveaway: {e}")
            return None
    
    async def add_giveaway_participant(self, message_id: int, user_id: int, entries: int = 1) -> bool:
        """Add participant to giveaway with bonus entries support"""
        try:
            async with self.get_connection() as conn:
                # Get current participants
                cursor = await conn.execute(
                    'SELECT participants FROM active_giveaways WHERE message_id = ?',
                    (message_id,)
                )
                result = await cursor.fetchone()

                if result:
                    # Try to parse as dict (new format), fallback to list (old format)
                    try:
                        participants_data = json.loads(result[0]) if result[0] else {}
                        if isinstance(participants_data, list):
                            # Convert old format to new format
                            participants_data = {str(uid): 1 for uid in participants_data}
                    except:
                        participants_data = {}

                    user_id_str = str(user_id)
                    if user_id_str not in participants_data:
                        participants_data[user_id_str] = entries
                        await conn.execute(
                            'UPDATE active_giveaways SET participants = ? WHERE message_id = ?',
                            (json.dumps(participants_data), message_id)
                        )
                        await conn.commit()
                        logger.debug(f"Added participant {user_id} with {entries} entries to giveaway {message_id}")
                        return True
                return False
        except Exception as e:
            logger.error(f"Error adding giveaway participant: {e}")
            return False

    async def remove_giveaway_participant(self, message_id: int, user_id: int) -> bool:
        """Remove participant from giveaway"""
        try:
            async with self.get_connection() as conn:
                # Get current participants
                cursor = await conn.execute(
                    'SELECT participants FROM active_giveaways WHERE message_id = ?',
                    (message_id,)
                )
                result = await cursor.fetchone()

                if result:
                    # Handle both old and new formats
                    try:
                        participants_data = json.loads(result[0]) if result[0] else {}
                        if isinstance(participants_data, list):
                            # Old format - convert and remove
                            if user_id in participants_data:
                                participants_data.remove(user_id)
                                participants_data = {str(uid): 1 for uid in participants_data}
                        else:
                            # New format - remove by key
                            user_id_str = str(user_id)
                            if user_id_str in participants_data:
                                del participants_data[user_id_str]
                    except:
                        participants_data = {}

                    await conn.execute(
                        'UPDATE active_giveaways SET participants = ? WHERE message_id = ?',
                        (json.dumps(participants_data), message_id)
                    )
                    await conn.commit()
                    logger.debug(f"Removed participant {user_id} from giveaway {message_id}")
                    return True
                return False
        except Exception as e:
            logger.error(f"Error removing giveaway participant: {e}")
            return False

    async def get_giveaway_participants_with_entries(self, message_id: int) -> dict:
        """Get giveaway participants with their entry counts"""
        try:
            async with self.get_connection() as conn:
                cursor = await conn.execute(
                    'SELECT participants FROM active_giveaways WHERE message_id = ?',
                    (message_id,)
                )
                result = await cursor.fetchone()

                if result and result[0]:
                    participants_data = json.loads(result[0])
                    # Handle both old and new formats
                    if isinstance(participants_data, list):
                        # Convert old format to new format
                        return {str(uid): 1 for uid in participants_data}
                    else:
                        # Already in new format
                        return participants_data
                return {}
        except Exception as e:
            logger.error(f"Error getting giveaway participants: {e}")
            return {}

    async def get_total_giveaway_entries(self, message_id: int) -> int:
        """Get total number of entries in a giveaway"""
        try:
            participants_data = await self.get_giveaway_participants_with_entries(message_id)
            return sum(int(entries) for entries in participants_data.values())
        except Exception as e:
            logger.error(f"Error getting total giveaway entries: {e}")
            return 0
    
    async def get_expired_giveaways(self) -> List[Dict[str, Any]]:
        """Get all expired giveaways"""
        try:
            async with self.get_connection() as conn:
                cursor = await conn.execute(
                    'SELECT * FROM active_giveaways WHERE end_time <= ?',
                    (datetime.now(),)
                )
                results = await cursor.fetchall()
                
                giveaways = []
                for result in results:
                    giveaways.append({
                        'message_id': result[0],
                        'guild_id': result[1],
                        'channel_id': result[2],
                        'title': result[3],
                        'description': result[4],
                        'end_time': result[5],
                        'winner_count': result[6],
                        'created_by': result[7],
                        'participants': json.loads(result[8]) if result[8] else []
                    })
                
                return giveaways
        except Exception as e:
            logger.error(f"Error getting expired giveaways: {e}")
            return []
    
    async def delete_giveaway(self, message_id: int) -> bool:
        """Delete giveaway from database"""
        try:
            async with self.get_connection() as conn:
                await conn.execute(
                    'DELETE FROM active_giveaways WHERE message_id = ?',
                    (message_id,)
                )
                await conn.commit()
                logger.debug(f"Deleted giveaway {message_id}")
                return True
        except Exception as e:
            logger.error(f"Error deleting giveaway: {e}")
            return False
    
    # Guild configuration methods
    async def update_guild_config(self, guild_id: int, **kwargs) -> bool:
        """Update guild configuration"""
        try:
            # Build dynamic query
            fields = []
            values = []
            
            for key, value in kwargs.items():
                if key in ['welcome_channel', 'activity_channel', 'activity_message_count',
                          'milestone_channel', 'milestone_count', 'last_member_count',
                          'nitro_emoji', 'robux_emoji', 'garden_emoji', 'custom_emoji',
                          'partner_servers', 'contact_message', 'contact_discord_server',
                          'contact_website', 'contact_email', 'contact_methods', 'auto_join_servers']:
                    fields.append(f"{key} = ?")
                    values.append(value)
            
            if not fields:
                return False

            # Use UPDATE with WHERE clause instead of INSERT OR REPLACE
            async with self.get_connection() as conn:
                # First, try to insert a new record if it doesn't exist
                await conn.execute('''
                    INSERT OR IGNORE INTO guild_config (guild_id, updated_at)
                    VALUES (?, ?)
                ''', (guild_id, datetime.now()))

                # Then update the specific fields
                fields.append("updated_at = ?")
                values.append(datetime.now())
                values.append(guild_id)

                await conn.execute(f'''
                    UPDATE guild_config
                    SET {', '.join(fields)}
                    WHERE guild_id = ?
                ''', values)

                await conn.commit()
                logger.debug(f"Updated guild config for {guild_id}: {kwargs}")
                return True
        except Exception as e:
            logger.error(f"Error updating guild config: {e}")
            return False
    
    async def get_guild_config(self, guild_id: int) -> Optional[Dict[str, Any]]:
        """Get guild configuration"""
        try:
            async with self.get_connection() as conn:
                # Use row_factory to get column names
                conn.row_factory = aiosqlite.Row
                cursor = await conn.execute(
                    'SELECT * FROM guild_config WHERE guild_id = ?',
                    (guild_id,)
                )
                result = await cursor.fetchone()

                if result:
                    # Convert Row to dict
                    config = dict(result)
                    return config
                return None
        except Exception as e:
            logger.error(f"Error getting guild config: {e}")
            return None

    # Partner servers methods
    async def get_partner_servers(self, guild_id: int) -> list:
        """Get list of partner server IDs for a guild"""
        try:
            config = await self.get_guild_config(guild_id)
            if config and config.get('partner_servers'):
                import json
                return json.loads(config['partner_servers'])
            return []
        except Exception as e:
            logger.error(f"Error getting partner servers: {e}")
            return []
    
    async def update_partner_servers(self, guild_id: int, server_ids: list) -> bool:
        """Update partner servers list for a guild"""
        try:
            import json
            return await self.update_guild_config(guild_id, partner_servers=json.dumps(server_ids))
        except Exception as e:
            logger.error(f"Error updating partner servers: {e}")
            return False

    # Role-based permission methods
    async def update_giveaway_participant_roles(self, guild_id: int, role_ids: list) -> bool:
        """Update giveaway participant roles for a guild"""
        try:
            import json
            async with self.get_connection() as conn:
                await conn.execute('''
                    INSERT OR REPLACE INTO guild_config (guild_id, giveaway_participant_roles)
                    VALUES (?, ?)
                    ON CONFLICT(guild_id) DO UPDATE SET
                    giveaway_participant_roles = excluded.giveaway_participant_roles,
                    updated_at = CURRENT_TIMESTAMP
                ''', (guild_id, json.dumps(role_ids)))
                await conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error updating giveaway participant roles: {e}")
            return False

    async def update_bot_admin_roles(self, guild_id: int, role_ids: list) -> bool:
        """Update bot admin roles for a guild"""
        try:
            import json
            async with self.get_connection() as conn:
                await conn.execute('''
                    INSERT OR REPLACE INTO guild_config (guild_id, bot_admin_roles)
                    VALUES (?, ?)
                    ON CONFLICT(guild_id) DO UPDATE SET
                    bot_admin_roles = excluded.bot_admin_roles,
                    updated_at = CURRENT_TIMESTAMP
                ''', (guild_id, json.dumps(role_ids)))
                await conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error updating bot admin roles: {e}")
            return False

    async def update_require_authorization(self, guild_id: int, require_auth: bool) -> bool:
        """Update authorization requirement for a guild"""
        try:
            async with self.get_connection() as conn:
                await conn.execute('''
                    INSERT OR REPLACE INTO guild_config (guild_id, require_authorization)
                    VALUES (?, ?)
                    ON CONFLICT(guild_id) DO UPDATE SET
                    require_authorization = excluded.require_authorization,
                    updated_at = CURRENT_TIMESTAMP
                ''', (guild_id, 1 if require_auth else 0))
                await conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error updating authorization requirement: {e}")
            return False

    # Contact support configuration methods
    async def get_contact_support_config(self, guild_id: int) -> dict:
        """Get contact support configuration for a guild"""
        try:
            async with self.get_connection() as conn:
                cursor = await conn.execute('''
                    SELECT contact_message, contact_discord_server, contact_website,
                           contact_email, contact_methods
                    FROM guild_config
                    WHERE guild_id = ?
                ''', (guild_id,))
                result = await cursor.fetchone()

                if result:
                    import json
                    contact_methods = json.loads(result[4]) if result[4] else []
                    return {
                        'contact_message': result[0],
                        'contact_discord_server': result[1],
                        'contact_website': result[2],
                        'contact_email': result[3],
                        'contact_methods': contact_methods
                    }
                else:
                    return {
                        'contact_message': None,
                        'contact_discord_server': None,
                        'contact_website': None,
                        'contact_email': None,
                        'contact_methods': []
                    }
        except Exception as e:
            logger.error(f"Error getting contact support config: {e}")
            return {
                'contact_message': None,
                'contact_discord_server': None,
                'contact_website': None,
                'contact_email': None,
                'contact_methods': []
            }

    async def update_contact_support_config(self, guild_id: int, **kwargs) -> bool:
        """Update contact support configuration for a guild"""
        try:
            import json

            # Prepare the data
            contact_data = {}
            for key in ['contact_message', 'contact_discord_server', 'contact_website', 'contact_email']:
                if key in kwargs:
                    contact_data[key] = kwargs[key]

            if 'contact_methods' in kwargs:
                contact_data['contact_methods'] = json.dumps(kwargs['contact_methods'])

            if not contact_data:
                return False

            async with self.get_connection() as conn:
                # First, ensure the guild record exists
                await conn.execute('''
                    INSERT OR IGNORE INTO guild_config (guild_id, updated_at)
                    VALUES (?, ?)
                ''', (guild_id, datetime.now()))

                # Build update query
                fields = []
                values = []
                for key, value in contact_data.items():
                    fields.append(f"{key} = ?")
                    values.append(value)

                fields.append("updated_at = ?")
                values.append(datetime.now())
                values.append(guild_id)

                await conn.execute(f'''
                    UPDATE guild_config
                    SET {', '.join(fields)}
                    WHERE guild_id = ?
                ''', values)

                await conn.commit()
                logger.debug(f"Updated contact support config for {guild_id}: {contact_data}")
                return True
        except Exception as e:
            logger.error(f"Error updating contact support config: {e}")
            return False

    async def get_giveaway_participant_roles(self, guild_id: int) -> list:
        """Get giveaway participant roles for a guild"""
        try:
            import json
            async with self.get_connection() as conn:
                cursor = await conn.execute(
                    'SELECT giveaway_participant_roles FROM guild_config WHERE guild_id = ?',
                    (guild_id,)
                )
                result = await cursor.fetchone()
                if result and result[0]:
                    return json.loads(result[0])
                return []
        except Exception as e:
            logger.error(f"Error getting giveaway participant roles: {e}")
            return []

    async def get_bot_admin_roles(self, guild_id: int) -> list:
        """Get bot admin roles for a guild"""
        try:
            import json
            async with self.get_connection() as conn:
                cursor = await conn.execute(
                    'SELECT bot_admin_roles FROM guild_config WHERE guild_id = ?',
                    (guild_id,)
                )
                result = await cursor.fetchone()
                if result and result[0]:
                    return json.loads(result[0])
                return []
        except Exception as e:
            logger.error(f"Error getting bot admin roles: {e}")
            return []

    async def get_require_authorization(self, guild_id: int) -> bool:
        """Get authorization requirement for a guild"""
        try:
            async with self.get_connection() as conn:
                cursor = await conn.execute(
                    'SELECT require_authorization FROM guild_config WHERE guild_id = ?',
                    (guild_id,)
                )
                result = await cursor.fetchone()
                if result is not None:
                    return bool(result[0])
                return True  # Default to requiring authorization
        except Exception as e:
            logger.error(f"Error getting authorization requirement: {e}")
            return True

    # Role bonus multiplier methods
    async def update_role_bonus_multipliers(self, guild_id: int, role_multipliers: dict) -> bool:
        """Update role bonus multipliers for a guild"""
        try:
            import json
            async with self.get_connection() as conn:
                await conn.execute('''
                    INSERT OR REPLACE INTO guild_config (guild_id, role_bonus_multipliers)
                    VALUES (?, ?)
                    ON CONFLICT(guild_id) DO UPDATE SET
                    role_bonus_multipliers = excluded.role_bonus_multipliers,
                    updated_at = CURRENT_TIMESTAMP
                ''', (guild_id, json.dumps(role_multipliers)))
                await conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error updating role bonus multipliers: {e}")
            return False

    async def get_role_bonus_multipliers(self, guild_id: int) -> dict:
        """Get role bonus multipliers for a guild"""
        try:
            import json
            async with self.get_connection() as conn:
                cursor = await conn.execute(
                    'SELECT role_bonus_multipliers FROM guild_config WHERE guild_id = ?',
                    (guild_id,)
                )
                result = await cursor.fetchone()
                if result and result[0]:
                    return json.loads(result[0])
                return {}
        except Exception as e:
            logger.error(f"Error getting role bonus multipliers: {e}")
            return {}

    async def add_role_bonus_multiplier(self, guild_id: int, role_id: int, multiplier: float) -> bool:
        """Add or update a single role bonus multiplier"""
        try:
            current_multipliers = await self.get_role_bonus_multipliers(guild_id)
            current_multipliers[str(role_id)] = multiplier
            return await self.update_role_bonus_multipliers(guild_id, current_multipliers)
        except Exception as e:
            logger.error(f"Error adding role bonus multiplier: {e}")
            return False

    async def remove_role_bonus_multiplier(self, guild_id: int, role_id: int) -> bool:
        """Remove a role bonus multiplier"""
        try:
            current_multipliers = await self.get_role_bonus_multipliers(guild_id)
            if str(role_id) in current_multipliers:
                del current_multipliers[str(role_id)]
                return await self.update_role_bonus_multipliers(guild_id, current_multipliers)
            return True
        except Exception as e:
            logger.error(f"Error removing role bonus multiplier: {e}")
            return False

    # Auto-join server configuration methods
    async def get_auto_join_servers(self, guild_id: int) -> dict:
        """Get auto-join server configuration for a guild"""
        try:
            import json
            async with self.get_connection() as conn:
                cursor = await conn.execute(
                    'SELECT auto_join_servers FROM guild_config WHERE guild_id = ?',
                    (guild_id,)
                )
                result = await cursor.fetchone()
                if result and result[0]:
                    return json.loads(result[0])
                return {}
        except Exception as e:
            logger.error(f"Error getting auto-join servers: {e}")
            return {}

    async def update_auto_join_servers(self, guild_id: int, auto_join_config: dict) -> bool:
        """Update auto-join server configuration for a guild"""
        try:
            import json
            return await self.update_guild_config(guild_id, auto_join_servers=json.dumps(auto_join_config))
        except Exception as e:
            logger.error(f"Error updating auto-join servers: {e}")
            return False

    async def is_auto_join_enabled(self, guild_id: int, server_id: int) -> bool:
        """Check if auto-join is enabled for a specific partner server"""
        try:
            auto_join_config = await self.get_auto_join_servers(guild_id)
            server_config = auto_join_config.get(str(server_id), {})
            return server_config.get('enabled', False)
        except Exception as e:
            logger.error(f"Error checking auto-join status: {e}")
            return False

# Global database manager instance
db_manager = DatabaseManager()
