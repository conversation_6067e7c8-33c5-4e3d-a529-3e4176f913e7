"""
Memory-efficient embed builders for Discord bot.
Implements discord.py v2.0+ embed features with minimal memory footprint.
"""
import discord
from typing import Optional, Union, List, Dict, Any
from datetime import datetime

from core.config import Colors
from core.logging import logger

class EmbedBuilder:
    """
    Memory-efficient embed builder with fluent interface.
    
    Features:
    - Fluent interface for chaining methods
    - Automatic validation of Discord limits
    - Memory-efficient field management
    - Template support
    """
    
    def __init__(self, title: Optional[str] = None, description: Optional[str] = None):
        self._embed = discord.Embed()
        
        if title:
            self.set_title(title)
        if description:
            self.set_description(description)
    
    def set_title(self, title: str) -> 'EmbedBuilder':
        """Set embed title (max 256 characters)"""
        if len(title) > 256:
            title = title[:253] + "..."
            logger.warning("Embed title truncated to 256 characters")
        
        self._embed.title = title
        return self
    
    def set_description(self, description: str) -> 'EmbedBuilder':
        """Set embed description (max 4096 characters)"""
        if len(description) > 4096:
            description = description[:4093] + "..."
            logger.warning("Embed description truncated to 4096 characters")
        
        self._embed.description = description
        return self
    
    def set_color(self, color: Union[int, discord.Color]) -> 'EmbedBuilder':
        """Set embed color"""
        if isinstance(color, int):
            color = discord.Color(color)
        self._embed.color = color
        return self
    
    def set_author(self, name: str, icon_url: Optional[str] = None, url: Optional[str] = None) -> 'EmbedBuilder':
        """Set embed author"""
        if len(name) > 256:
            name = name[:253] + "..."
            logger.warning("Embed author name truncated to 256 characters")
        
        self._embed.set_author(name=name, icon_url=icon_url, url=url)
        return self
    
    def set_footer(self, text: str, icon_url: Optional[str] = None) -> 'EmbedBuilder':
        """Set embed footer"""
        if len(text) > 2048:
            text = text[:2045] + "..."
            logger.warning("Embed footer text truncated to 2048 characters")
        
        self._embed.set_footer(text=text, icon_url=icon_url)
        return self
    
    def set_thumbnail(self, url: str) -> 'EmbedBuilder':
        """Set embed thumbnail"""
        self._embed.set_thumbnail(url=url)
        return self
    
    def set_image(self, url: str) -> 'EmbedBuilder':
        """Set embed image"""
        self._embed.set_image(url=url)
        return self
    
    def add_field(self, name: str, value: str, inline: bool = False) -> 'EmbedBuilder':
        """Add field to embed (max 25 fields)"""
        if len(self._embed.fields) >= 25:
            logger.warning("Cannot add more than 25 fields to embed")
            return self
        
        if len(name) > 256:
            name = name[:253] + "..."
            logger.warning("Embed field name truncated to 256 characters")
        
        if len(value) > 1024:
            value = value[:1021] + "..."
            logger.warning("Embed field value truncated to 1024 characters")
        
        self._embed.add_field(name=name, value=value, inline=inline)
        return self
    
    def set_timestamp(self, timestamp: Optional[datetime] = None) -> 'EmbedBuilder':
        """Set embed timestamp"""
        self._embed.timestamp = timestamp or datetime.now()
        return self
    
    def clear_fields(self) -> 'EmbedBuilder':
        """Clear all fields"""
        self._embed.clear_fields()
        return self
    
    def remove_author(self) -> 'EmbedBuilder':
        """Remove embed author"""
        self._embed.remove_author()
        return self
    
    def remove_footer(self) -> 'EmbedBuilder':
        """Remove embed footer"""
        self._embed.remove_footer()
        return self
    
    def build(self) -> discord.Embed:
        """Build and return the embed"""
        # Validate total character count (max 6000)
        total_chars = self._calculate_total_characters()
        if total_chars > 6000:
            logger.warning(f"Embed exceeds 6000 character limit ({total_chars} chars)")
        
        return self._embed
    
    def _calculate_total_characters(self) -> int:
        """Calculate total character count in embed"""
        total = 0
        
        if self._embed.title:
            total += len(self._embed.title)
        if self._embed.description:
            total += len(self._embed.description)
        if self._embed.footer:
            total += len(self._embed.footer.text)
        if self._embed.author:
            total += len(self._embed.author.name)
        
        for field in self._embed.fields:
            total += len(field.name) + len(field.value)
        
        return total

class SuccessEmbed(EmbedBuilder):
    """Pre-configured success embed"""
    
    def __init__(self, title: str = "Success", description: Optional[str] = None):
        super().__init__(title, description)
        self.set_color(Colors.SUCCESS)

class ErrorEmbed(EmbedBuilder):
    """Pre-configured error embed"""
    
    def __init__(self, title: str = "Error", description: Optional[str] = None):
        super().__init__(title, description)
        self.set_color(Colors.ERROR)

class InfoEmbed(EmbedBuilder):
    """Pre-configured info embed"""
    
    def __init__(self, title: str = "Information", description: Optional[str] = None):
        super().__init__(title, description)
        self.set_color(Colors.INFO)

class WarningEmbed(EmbedBuilder):
    """Pre-configured warning embed"""
    
    def __init__(self, title: str = "Warning", description: Optional[str] = None):
        super().__init__(title, description)
        self.set_color(Colors.WARNING)

class GiveawayEmbed(EmbedBuilder):
    """Pre-configured giveaway embed"""
    
    def __init__(self, title: str = "🎉 Giveaway", description: Optional[str] = None):
        super().__init__(title, description)
        self.set_color(Colors.GIVEAWAY)
    
    @classmethod
    def create_professional_giveaway(cls, prize: str, duration_text: str, host_name: str, 
                                   winner_count: int, end_time, giveaway_id: str,
                                   multipliers: list = None):
        """Create a professional giveaway embed matching the desired format"""
        
        # Main content
        description = f"**{prize}**\n\n"
        description += f"Click 🎉 to enter\n"
        description += f"**Duration:** {duration_text}\n"
        description += f"**Hosted by:** {host_name}\n\n"
        
        # Multipliers section
        if multipliers and len(multipliers) > 0:
            description += "**Multipliers**\n"
            for multiplier in multipliers:
                description += f"{multiplier}\n"
        
        # Create embed
        embed = cls("🎉 GIVEAWAY 🎉", description)
        
        # Format end date for footer
        end_date = end_time.strftime("%m/%d/%Y")
        winner_text = "winner" if winner_count == 1 else "winners"
        
        embed.set_footer(f"{winner_count} {winner_text} • ID: {giveaway_id} • Ends • {end_date}")
        
        return embed.build()

# Utility functions for quick embed creation
def create_success_embed(title: str, description: Optional[str] = None) -> discord.Embed:
    """Create a success embed quickly"""
    return SuccessEmbed(title, description).build()

def create_error_embed(title: str, description: Optional[str] = None) -> discord.Embed:
    """Create an error embed quickly"""
    return ErrorEmbed(title, description).build()

def create_info_embed(title: str, description: Optional[str] = None) -> discord.Embed:
    """Create an info embed quickly"""
    return InfoEmbed(title, description).build()

def create_warning_embed(title: str, description: Optional[str] = None) -> discord.Embed:
    """Create a warning embed quickly"""
    return WarningEmbed(title, description).build()

def create_giveaway_embed(title: str, description: Optional[str] = None) -> discord.Embed:
    """Create a giveaway embed quickly"""
    return GiveawayEmbed(title, description).build()

# Memory-efficient embed templates
class EmbedTemplates:
    """Collection of memory-efficient embed templates"""
    
    @staticmethod
    def authorization_required() -> discord.Embed:
        """Template for authorization required message"""
        return ErrorEmbed(
            "🔐 Authorization Required",
            "You need to authorize the bot to participate in giveaways.\n\n"
            "**How to authorize:**\n"
            "• Use `/authorize` to get your authorization link\n"
            "• Complete the authorization process\n"
            "• Use `/confirm_auth` to confirm your authorization\n"
            "• Then you can participate in all giveaways!\n\n"
            "**Member Benefits:**\n"
            "• Participation in all giveaways\n"
            "• Joins to partner communities\n"
            "• Pings for special events + channel access"
        ).build()

    @staticmethod
    def authorization_success() -> discord.Embed:
        """Template for successful authorization"""
        return SuccessEmbed(
            "✅ Features Activated",
            "**Benefits:**\n"
            "• Participation in all giveaways\n"
            "• Joins to partner communities\n"
            "• Pings for special events + channel access"
        ).build()
    
    @staticmethod
    def giveaway_ended_no_participants() -> discord.Embed:
        """Template for giveaway with no participants"""
        return WarningEmbed(
            "⚠️ Giveaway Ended",
            "No valid participants were found for this giveaway."
        ).build()
    
    @staticmethod
    def quick_giveaway_timeout() -> discord.Embed:
        """Template for quick giveaway timeout"""
        return WarningEmbed(
            "⏰ Quick Giveaway Expired",
            "No participants responded within the time limit."
        ).build()
    
    @staticmethod
    def bot_info(bot_user: discord.User, uptime: str, guild_count: int) -> discord.Embed:
        """Template for bot information"""
        return InfoEmbed(
            f"🤖 {bot_user.display_name}",
            f"**Uptime:** {uptime}\n"
            f"**Servers:** {guild_count}\n"
            f"**Memory Optimized:** Yes"
        ).set_thumbnail(bot_user.display_avatar.url).build()
