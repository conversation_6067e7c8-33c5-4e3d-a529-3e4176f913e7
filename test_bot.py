#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de verificación para el bot de Discord
Verifica que todos los componentes estén configurados correctamente
"""

import os
import sys
import sqlite3
from pathlib import Path

def test_database():
    """Verificar conexión y estructura de la base de datos"""
    print("🔍 Verificando base de datos...")
    
    db_path = "bot_data.db"
    if not os.path.exists(db_path):
        print("  ⚠️  Base de datos no encontrada, se creará al ejecutar el bot")
        return True
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Verificar tablas esperadas
        expected_tables = ['authorized_users', 'active_giveaways', 'guild_config']
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        existing_tables = [row[0] for row in cursor.fetchall()]
        
        missing_tables = [table for table in expected_tables if table not in existing_tables]
        
        if missing_tables:
            print(f"  ⚠️  Tablas faltantes: {', '.join(missing_tables)} (se crearán automáticamente)")
        else:
            print("  ✅ Todas las tablas de la base de datos están presentes")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"  ❌ Error de base de datos: {e}")
        return False

def test_project_files():
    """Verificar que todos los archivos del proyecto estén presentes"""
    print("🔍 Verificando archivos del proyecto...")
    
    required_files = [
        'main.py',
        'core/bot.py',
        'config/messages.py',
        'utils/oauth_server.py',
        'utils/database.py',
        'requirements.txt',
        '.env',
        'SETUP_GUIDE.md'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"  ❌ Archivos faltantes: {', '.join(missing_files)}")
        return False
    else:
        print("  ✅ Todos los archivos del proyecto están presentes")
        return True

def test_environment_config():
    """Verificar configuración de variables de entorno"""
    print("🔍 Verificando configuración de entorno...")
    
    if not os.path.exists('.env'):
        print("  ❌ Archivo .env no encontrado")
        print("  💡 Copia .env.example a .env y configura tus valores")
        return False
    
    # Cargar variables de entorno
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        print("  ❌ python-dotenv no está instalado")
        return False
    
    # Verificar variables requeridas
    required_vars = {
        'DISCORD_TOKEN': 'Token del bot de Discord',
        'APPLICATION_ID': 'ID de la aplicación de Discord',
        'DISCORD_CLIENT_SECRET': 'Client Secret para OAuth2',
        'REDIRECT_URI': 'URI de redirección para OAuth2'
    }
    
    missing_vars = []
    placeholder_vars = []
    
    for var, description in required_vars.items():
        value = os.getenv(var)
        if not value:
            missing_vars.append(f"{var} ({description})")
        elif 'your_' in value.lower() or 'here' in value.lower():
            placeholder_vars.append(f"{var} ({description})")
    
    if missing_vars:
        print(f"  ❌ Variables de entorno faltantes: {', '.join(missing_vars)}")
        return False
    
    if placeholder_vars:
        print(f"  ⚠️  Variables con valores de ejemplo: {', '.join(placeholder_vars)}")
        print("  💡 Actualiza estos valores con tus credenciales reales")
        return False
    
    # Verificar formato del token
    token = os.getenv('DISCORD_TOKEN')
    if token and not (token.startswith('MT') or token.startswith('OT') or token.startswith('NZ')):
        print("  ⚠️  El formato del token parece incorrecto")
        print("  💡 Verifica que hayas copiado el token completo")
    
    # Verificar formato de REDIRECT_URI
    redirect_uri = os.getenv('REDIRECT_URI')
    if redirect_uri and not redirect_uri.startswith('http'):
        print("  ⚠️  REDIRECT_URI debe comenzar con http:// o https://")
        return False
    
    print("  ✅ Configuración de entorno correcta")
    return True

def test_dependencies():
    """Verificar que todas las dependencias estén instaladas"""
    print("🔍 Verificando dependencias de Python...")
    
    required_packages = {
        'discord': 'discord.py',
        'aiosqlite': 'aiosqlite',
        'dotenv': 'python-dotenv',
        'aiohttp': 'aiohttp'
    }
    
    missing_packages = []
    
    for package, pip_name in required_packages.items():
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(pip_name)
    
    if missing_packages:
        print(f"  ❌ Paquetes faltantes: {', '.join(missing_packages)}")
        print(f"  💡 Instala con: pip install {' '.join(missing_packages)}")
        return False
    else:
        print("  ✅ Todas las dependencias están instaladas")
        return True

def test_oauth_config():
    """Verificar configuración específica de OAuth2"""
    print("🔍 Verificando configuración OAuth2...")
    
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        client_secret = os.getenv('DISCORD_CLIENT_SECRET')
        redirect_uri = os.getenv('REDIRECT_URI')
        
        if not client_secret:
            print("  ❌ DISCORD_CLIENT_SECRET no configurado")
            print("  💡 Obtén el Client Secret del Discord Developer Portal")
            return False
        
        if not redirect_uri:
            print("  ❌ REDIRECT_URI no configurado")
            print("  💡 Configura REDIRECT_URI (ej: http://localhost:8080/callback)")
            return False
        
        if 'localhost:8080' in redirect_uri:
            print("  ✅ Configuración OAuth2 para desarrollo local")
        else:
            print(f"  ✅ Configuración OAuth2 personalizada: {redirect_uri}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error verificando OAuth2: {e}")
        return False

def main():
    """Ejecutar todas las verificaciones"""
    print("🤖 Verificador de Configuración del Bot de Discord")
    print("=" * 50)
    
    tests = [
        ("Archivos del Proyecto", test_project_files),
        ("Configuración de Entorno", test_environment_config),
        ("Configuración OAuth2", test_oauth_config),
        ("Dependencias de Python", test_dependencies),
        ("Base de Datos", test_database)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        if test_func():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Resultados: {passed}/{total} pruebas pasadas")
    
    if passed == total:
        print("\n🎉 ¡Todas las verificaciones pasaron!")
        print("\n📝 Próximos pasos:")
        print("1. Habilita los intents privilegiados en Discord Developer Portal")
        print("2. Configura 'Public Bot' y 'Guild Install' temporalmente")
        print("3. Ejecuta el bot: python main.py")
        print("4. Usa /authorize para configurar OAuth2")
        return True
    else:
        print("\n❌ Algunas verificaciones fallaron")
        print("\n🔧 Soluciona los problemas indicados arriba y vuelve a ejecutar")
        print("\n📖 Consulta SETUP_GUIDE.md para instrucciones detalladas")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)