#!/usr/bin/env python3
"""
Memory-optimized Discord bot launcher.
Simple entry point for the refactored bot with memory constraints.
"""
import sys
import os

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import and run the main application
from main import main
import asyncio

if __name__ == "__main__":
    print("🤖 Starting Memory-Optimized Discord Bot...")
    print("📊 Memory optimization features:")
    print("   • Message cache disabled (max_messages=None)")
    print("   • Minimal intents configuration")
    print("   • Automatic garbage collection")
    print("   • Efficient view cleanup")
    print("   • Connection pooling")
    print()
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Bot stopped by user")
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        sys.exit(1)
