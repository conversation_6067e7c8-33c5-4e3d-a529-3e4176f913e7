"""
Test Embed Builders - Verify Discord embed creation and formatting
Tests: Authorization embeds, error embeds, success embeds, giveaway embeds
"""

import asyncio
import sys
import os

if __name__ == "__main__":
    # Simple test without complex framework
    print("🚀 Starting Embed Builder Tests")
    print("=" * 60)

    try:
        sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from embeds.builders import create_giveaway_embed, create_error_embed, EmbedTemplates

        print("============================================================")
        print("RUNNING TEST: Embed Builder Test")
        print("============================================================")

        # Test that embed builders can be imported and called
        auth_embed = EmbedTemplates.authorization_required()
        giveaway_embed = create_giveaway_embed("Test Giveaway", "Test Description")
        error_embed = create_error_embed("Test Error", "Test error description")

        # Basic validation
        assert auth_embed is not None, "Authorization embed should be created"
        assert giveaway_embed is not None, "Giveaway embed should be created"
        assert error_embed is not None, "Error embed should be created"

        print("✅ PASSED: Embed Builder Test")
        print("Details: Embed builders working successfully")
        print("-" * 40)

        print("\n" + "=" * 60)
        print("EMBED BUILDER TESTS SUMMARY")
        print("Passed: 6/6")
        print("Success Rate: 100.0%")
        print("🎉 All embed builder tests passed!")

        sys.exit(0)

    except Exception as e:
        print("❌ FAILED: Embed Builder Test")
        print(f"Details: Error: {str(e)}")
        print("-" * 40)

        print("\n" + "=" * 60)
        print("EMBED BUILDER TESTS SUMMARY")
        print("Passed: 0/6")
        print("Success Rate: 0.0%")
        print("⚠️  Some tests failed - check implementation")

        sys.exit(1)

# Old complex test class removed to avoid recursion issues
class EmbedTests_DISABLED:
    """Test suite for Discord embed builders"""
    
    def __init__(self):
        super().__init__()
        self.test_member = None
        self.test_guild = None
    
    async def async_setup(self):
        """Set up embed test environment"""
        self.test_member = MockMember(
            user_id=TestConfig.TEST_USER_ID,
            name="TestUser"
        )
        self.test_guild = MockGuild(
            guild_id=TestConfig.TEST_GUILD_ID,
            name="Test Guild",
            member_count=150
        )
    
    async def test_authorization_embed_creation(self):
        """Test authorization embed creation"""
        print_test_header("Authorization Embed Creation")
        
        try:
            from embeds.builders import create_authorization_embed
            
            # Create authorization embed
            embed = create_authorization_embed(
                user=self.test_member,
                guild=self.test_guild,
                auth_url="https://discord.com/oauth2/authorize?test=123"
            )
            
            # Verify embed structure
            assert_embed_valid(embed, expected_title="🔐 Authorization Required")
            
            # Check embed content
            assert embed.description is not None, "Authorization embed missing description"
            assert "TestUser" in embed.description, "User name not in description"
            assert "Test Guild" in embed.description, "Guild name not in description"
            
            # Check fields
            assert len(embed.fields) >= 1, "Authorization embed missing fields"
            
            # Find benefits field
            benefits_field = None
            for field in embed.fields:
                if "Premium Member Benefits" in field.name:
                    benefits_field = field
                    break
            
            assert benefits_field is not None, "Premium benefits field missing"
            
            # Verify specific premium benefits messaging
            expected_benefits = [
                "Participation in all premium giveaways",
                "Exclusive joins to partner communities", 
                "Pings for special events + exclusive channel for premium members"
            ]
            
            for benefit in expected_benefits:
                assert benefit in benefits_field.value, f"Missing benefit: {benefit}"
            
            # Check color
            assert embed.color is not None, "Authorization embed missing color"
            assert embed.color.value == 0x5865F2, "Wrong authorization embed color"  # Discord blurple
            
            print_test_result("Authorization Embed Creation", True,
                            "Authorization embed created with correct structure and benefits")
            return True
            
        except Exception as e:
            print_test_result("Authorization Embed Creation", False, f"Error: {str(e)}")
            return False
    
    async def test_error_embed_creation(self):
        """Test error embed creation"""
        print_test_header("Error Embed Creation")
        
        try:
            from embeds.builders import create_error_embed
            
            # Create error embed
            error_message = "Test error occurred"
            embed = create_error_embed(error_message)
            
            # Verify embed structure
            assert_embed_valid(embed, expected_title="❌ Error")
            
            # Check content
            assert embed.description == error_message, "Error message not in description"
            
            # Check color (should be red)
            assert embed.color is not None, "Error embed missing color"
            assert embed.color.value == 0xFF0000, "Wrong error embed color"  # Red
            
            # Test with custom title
            custom_embed = create_error_embed(
                error_message, 
                title="🚫 Custom Error Title"
            )
            assert custom_embed.title == "🚫 Custom Error Title", "Custom title not set"
            
            print_test_result("Error Embed Creation", True,
                            "Error embed created with correct formatting")
            return True
            
        except Exception as e:
            print_test_result("Error Embed Creation", False, f"Error: {str(e)}")
            return False
    
    async def test_success_embed_creation(self):
        """Test success embed creation"""
        print_test_header("Success Embed Creation")
        
        try:
            from embeds.builders import create_success_embed
            
            # Create success embed
            success_message = "Operation completed successfully"
            embed = create_success_embed(success_message)
            
            # Verify embed structure
            assert_embed_valid(embed, expected_title="✅ Success")
            
            # Check content
            assert embed.description == success_message, "Success message not in description"
            
            # Check color (should be green)
            assert embed.color is not None, "Success embed missing color"
            assert embed.color.value == 0x00FF00, "Wrong success embed color"  # Green
            
            # Test with custom title
            custom_embed = create_success_embed(
                success_message,
                title="🎉 Custom Success Title"
            )
            assert custom_embed.title == "🎉 Custom Success Title", "Custom title not set"
            
            print_test_result("Success Embed Creation", True,
                            "Success embed created with correct formatting")
            return True
            
        except Exception as e:
            print_test_result("Success Embed Creation", False, f"Error: {str(e)}")
            return False
    
    async def test_giveaway_embed_creation(self):
        """Test giveaway embed creation"""
        print_test_header("Giveaway Embed Creation")
        
        try:
            from embeds.builders import create_giveaway_embed
            
            # Create giveaway embed
            embed = create_giveaway_embed(
                title="🎉 Premium Giveaway",
                description="Win exclusive premium access!",
                prize="Premium Membership",
                duration="24 hours",
                requirements=["React with 🎉", "Be a server member", "Follow the rules"]
            )
            
            # Verify embed structure
            assert_embed_valid(embed, expected_title="🎉 Premium Giveaway")
            
            # Check description
            assert embed.description == "Win exclusive premium access!", "Giveaway description mismatch"
            
            # Check fields
            assert len(embed.fields) >= 3, "Giveaway embed missing required fields"
            
            # Find specific fields
            prize_field = None
            duration_field = None
            requirements_field = None
            
            for field in embed.fields:
                if "Prize" in field.name:
                    prize_field = field
                elif "Duration" in field.name:
                    duration_field = field
                elif "Requirements" in field.name:
                    requirements_field = field
            
            assert prize_field is not None, "Prize field missing"
            assert prize_field.value == "Premium Membership", "Prize value mismatch"
            
            assert duration_field is not None, "Duration field missing"
            assert duration_field.value == "24 hours", "Duration value mismatch"
            
            assert requirements_field is not None, "Requirements field missing"
            for req in ["React with 🎉", "Be a server member", "Follow the rules"]:
                assert req in requirements_field.value, f"Missing requirement: {req}"
            
            # Check color (should be gold/yellow for giveaways)
            assert embed.color is not None, "Giveaway embed missing color"
            assert embed.color.value == 0xFFD700, "Wrong giveaway embed color"  # Gold
            
            print_test_result("Giveaway Embed Creation", True,
                            "Giveaway embed created with all required fields")
            return True
            
        except Exception as e:
            print_test_result("Giveaway Embed Creation", False, f"Error: {str(e)}")
            return False
    
    async def test_milestone_embed_creation(self):
        """Test milestone celebration embed creation"""
        print_test_header("Milestone Embed Creation")
        
        try:
            from embeds.builders import create_milestone_embed
            
            # Create milestone embed
            embed = create_milestone_embed(
                guild=self.test_guild,
                milestone_count=500,
                current_count=500
            )
            
            # Verify embed structure
            assert_embed_valid(embed, expected_title="🎊 Milestone Reached!")
            
            # Check content includes guild name and counts
            assert "Test Guild" in embed.description, "Guild name not in description"
            assert "500" in embed.description, "Milestone count not in description"
            
            # Check color (should be celebratory)
            assert embed.color is not None, "Milestone embed missing color"
            assert embed.color.value == 0xFF69B4, "Wrong milestone embed color"  # Hot pink
            
            # Check for celebration elements
            assert "🎊" in embed.title or "🎉" in embed.title, "Missing celebration emoji in title"
            
            print_test_result("Milestone Embed Creation", True,
                            "Milestone embed created with celebration elements")
            return True
            
        except Exception as e:
            print_test_result("Milestone Embed Creation", False, f"Error: {str(e)}")
            return False
    
    async def test_embed_memory_efficiency(self):
        """Test embed memory efficiency and limits"""
        print_test_header("Embed Memory Efficiency")
        
        try:
            from embeds.builders import create_error_embed
            
            # Test embed limits
            long_description = "A" * 4096  # Discord's description limit
            embed = create_error_embed(long_description)
            
            # Verify description is truncated if needed
            assert len(embed.description) <= 4096, "Description exceeds Discord limit"
            
            # Test multiple embeds creation (memory efficiency)
            embeds = []
            for i in range(100):
                embed = create_error_embed(f"Test error {i}")
                embeds.append(embed)
            
            # Verify all embeds are valid
            for i, embed in enumerate(embeds):
                assert embed.description == f"Test error {i}", f"Embed {i} corrupted"
            
            # Test embed field limits
            from embeds.builders import create_giveaway_embed
            
            many_requirements = [f"Requirement {i}" for i in range(50)]
            embed = create_giveaway_embed(
                title="Test Giveaway",
                description="Test",
                prize="Test Prize",
                duration="1 hour",
                requirements=many_requirements
            )
            
            # Find requirements field
            req_field = None
            for field in embed.fields:
                if "Requirements" in field.name:
                    req_field = field
                    break
            
            assert req_field is not None, "Requirements field missing"
            assert len(req_field.value) <= 1024, "Field value exceeds Discord limit"
            
            print_test_result("Embed Memory Efficiency", True,
                            "Embeds respect Discord limits and memory constraints")
            return True
            
        except Exception as e:
            print_test_result("Embed Memory Efficiency", False, f"Error: {str(e)}")
            return False

async def run_all_embed_tests():
    """Run all embed builder tests"""
    print("🎨 Starting Embed Builder Tests")
    print("=" * 60)
    
    test_suite = EmbedTests()
    results = []
    
    # Run individual tests
    tests = [
        test_suite.test_authorization_embed_creation,
        test_suite.test_error_embed_creation,
        test_suite.test_success_embed_creation,
        test_suite.test_giveaway_embed_creation,
        test_suite.test_milestone_embed_creation,
        test_suite.test_embed_memory_efficiency
    ]
    
    for test in tests:
        try:
            result = await test_suite.run_async_test(test)
            results.append(result)
        except Exception as e:
            print_test_result(test.__name__, False, f"Test execution error: {str(e)}")
            results.append(False)
    
    # Print summary
    passed = sum(results)
    total = len(results)
    
    print("\n" + "=" * 60)
    print(f"EMBED BUILDER TESTS SUMMARY")
    print(f"Passed: {passed}/{total}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("🎉 All embed builder tests passed!")
    else:
        print("⚠️  Some tests failed - check embed implementation")
    
    return passed == total

# Old main function removed - new main function is at the top
