#!/usr/bin/env python3
"""
Test script to verify analytics functions work without division by zero errors.
This script tests the fixed analytics functions with empty user collections.
"""

import asyncio
import sys
import os
from unittest.mock import AsyncMock, MagicMock

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_analytics_with_empty_users():
    """Test analytics functions with empty user collections"""
    print("🧪 Testing Analytics Functions with Empty User Collections")
    print("=" * 60)
    
    try:
        # Mock the database manager to return empty user list
        from utils import analytics
        from utils import oauth_advanced
        
        # Create mock database manager
        mock_db_manager = AsyncMock()
        mock_db_manager.get_authorized_users.return_value = []  # Empty user list
        mock_db_manager.get_guild_config.return_value = None
        
        # Replace the real db_manager with our mock
        analytics.db_manager = mock_db_manager
        oauth_advanced.db_manager = mock_db_manager
        
        print("✅ Mock database manager configured")
        
        # Test 1: User Engagement Metrics
        print("\n📊 Testing User Engagement Metrics...")
        try:
            engagement_metrics = await analytics.SmartAnalytics.get_user_engagement_metrics(12345)
            print(f"   Result: {engagement_metrics}")
            
            # Verify no division by zero occurred
            assert engagement_metrics['oauth_health_percentage'] == 0
            assert engagement_metrics['total_authorized_users'] == 0
            print("   ✅ User engagement metrics handled empty users correctly")
            
        except Exception as e:
            print(f"   ❌ Error in user engagement metrics: {e}")
            return False
        
        # Test 2: Insights Generation
        print("\n💡 Testing Insights Generation...")
        try:
            insights = await analytics.SmartAnalytics.generate_insights_and_recommendations(12345)
            print(f"   Result: {insights}")
            
            # Should return helpful message for empty users
            assert len(insights) > 0
            assert "Getting Started" in insights[0]
            print("   ✅ Insights generation handled empty users correctly")
            
        except Exception as e:
            print(f"   ❌ Error in insights generation: {e}")
            return False
        
        # Test 3: OAuth Advanced Analytics (simulate the monitoring function)
        print("\n🔐 Testing OAuth Advanced Analytics...")
        try:
            # Create a mock token manager
            token_manager = oauth_advanced.AdvancedTokenManager()
            
            # Test the analytics generation method directly
            await token_manager._generate_analytics()
            print("   ✅ OAuth advanced analytics handled empty users correctly")
            
        except Exception as e:
            print(f"   ❌ Error in OAuth advanced analytics: {e}")
            return False
        
        # Test 4: Analytics Embed Creation
        print("\n📈 Testing Analytics Embed Creation...")
        try:
            embed = await analytics.SmartAnalytics.create_analytics_embed(12345)
            print(f"   Embed title: {embed.title}")
            print(f"   Embed fields: {len(embed.fields)}")
            print("   ✅ Analytics embed creation handled empty users correctly")
            
        except Exception as e:
            print(f"   ❌ Error in analytics embed creation: {e}")
            return False
        
        print("\n" + "=" * 60)
        print("🎉 ALL TESTS PASSED! Analytics functions are now safe from division by zero errors.")
        return True
        
    except Exception as e:
        print(f"\n❌ CRITICAL ERROR: {e}")
        return False

async def test_analytics_with_sample_users():
    """Test analytics functions with sample user data"""
    print("\n🧪 Testing Analytics Functions with Sample User Data")
    print("=" * 60)
    
    try:
        from utils import analytics
        from utils import oauth_advanced
        
        # Mock database manager with sample data
        mock_db_manager = AsyncMock()
        mock_db_manager.get_authorized_users.return_value = [
            (123456789, "TestUser1"),
            (987654321, "TestUser2"),
            (555666777, "TestUser3")
        ]
        mock_db_manager.get_user_oauth_tokens.return_value = {
            'access_token': 'sample_token',
            'token_expires_at': '2025-12-31T23:59:59'
        }
        mock_db_manager.get_guild_config.return_value = {'welcome_channel': 123}
        
        # Replace the real db_manager with our mock
        analytics.db_manager = mock_db_manager
        oauth_advanced.db_manager = mock_db_manager
        
        print("✅ Mock database manager configured with sample data")
        
        # Test with actual user data
        print("\n📊 Testing User Engagement Metrics with Data...")
        engagement_metrics = await analytics.SmartAnalytics.get_user_engagement_metrics(12345)
        print(f"   OAuth Health: {engagement_metrics['oauth_health_percentage']:.1f}%")
        print(f"   Total Users: {engagement_metrics['total_authorized_users']}")
        print("   ✅ Metrics calculated correctly with user data")
        
        print("\n🔐 Testing OAuth Advanced Analytics with Data...")
        token_manager = oauth_advanced.AdvancedTokenManager()
        await token_manager._generate_analytics()
        print("   ✅ OAuth analytics calculated correctly with user data")
        
        print("\n" + "=" * 60)
        print("🎉 SAMPLE DATA TESTS PASSED! Analytics work correctly with real data too.")
        return True
        
    except Exception as e:
        print(f"\n❌ ERROR WITH SAMPLE DATA: {e}")
        return False

async def main():
    """Main test function"""
    print("🚀 Starting Analytics Division by Zero Fix Verification")
    print("=" * 80)
    
    # Test 1: Empty users (the main fix)
    test1_passed = await test_analytics_with_empty_users()
    
    # Test 2: Sample users (ensure we didn't break normal functionality)
    test2_passed = await test_analytics_with_sample_users()
    
    print("\n" + "=" * 80)
    if test1_passed and test2_passed:
        print("🎉 ALL TESTS PASSED! The division by zero fix is working correctly.")
        print("✅ Bot startup should now work without analytics errors.")
        return 0
    else:
        print("❌ SOME TESTS FAILED! Please review the errors above.")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
