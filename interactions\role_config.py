"""
Role configuration modal for setting up role-based permissions
"""
import discord
import json
from utils.database import db_manager
from embeds.builders import create_success_embed, create_error_embed
from core.logging import logger

class RoleConfigModal(discord.ui.Modal):
    """Modal for configuring role-based permissions"""
    
    def __init__(self, guild_id: int, current_config: dict = None):
        super().__init__(title="🔐 Role-Based Permissions Configuration")
        self.guild_id = guild_id
        
        # Get current role IDs as comma-separated strings
        giveaway_roles = current_config.get('giveaway_participant_roles', []) if current_config else []
        admin_roles = current_config.get('bot_admin_roles', []) if current_config else []
        require_auth = current_config.get('require_authorization', True) if current_config else True
        
        # Convert role IDs to comma-separated string
        giveaway_roles_str = ', '.join(str(role_id) for role_id in giveaway_roles)
        admin_roles_str = ', '.join(str(role_id) for role_id in admin_roles)
        
        # Giveaway participant roles
        self.giveaway_roles_input = discord.ui.TextInput(
            label="🎉 Giveaway Participant Roles",
            placeholder="Enter role IDs separated by commas (e.g., 123456789, 987654321)",
            default=giveaway_roles_str,
            style=discord.TextStyle.short,
            max_length=500,
            required=False
        )
        self.add_item(self.giveaway_roles_input)
        
        # Bot admin roles
        self.admin_roles_input = discord.ui.TextInput(
            label="🔧 Bot Admin Roles",
            placeholder="Enter role IDs separated by commas (e.g., 123456789, 987654321)",
            default=admin_roles_str,
            style=discord.TextStyle.short,
            max_length=500,
            required=False
        )
        self.add_item(self.admin_roles_input)
        
        # Authorization requirement
        self.require_auth_input = discord.ui.TextInput(
            label="🔐 Require Authorization",
            placeholder="true or false - whether users must also complete /authorize",
            default="true" if require_auth else "false",
            style=discord.TextStyle.short,
            max_length=5,
            required=True
        )
        self.add_item(self.require_auth_input)
        
        # Help text
        self.help_text = discord.ui.TextInput(
            label="ℹ️ How to get Role IDs",
            placeholder="Enable Developer Mode in Discord settings, right-click role, Copy ID",
            default="Leave fields empty to remove restrictions. Server owner always has admin access.",
            style=discord.TextStyle.paragraph,
            max_length=200,
            required=False
        )
        self.add_item(self.help_text)
    
    @classmethod
    async def create(cls, guild_id: int):
        """Create modal with current configuration"""
        try:
            # Get current configuration
            config = await db_manager.get_guild_config(guild_id)
            
            # Parse role configurations
            current_config = {}
            if config:
                try:
                    current_config['giveaway_participant_roles'] = json.loads(config.get('giveaway_participant_roles', '[]'))
                    current_config['bot_admin_roles'] = json.loads(config.get('bot_admin_roles', '[]'))
                    current_config['require_authorization'] = bool(config.get('require_authorization', 1))
                except (json.JSONDecodeError, TypeError):
                    logger.warning(f"Failed to parse role config for guild {guild_id}")
                    current_config = {}
            
            return cls(guild_id, current_config)
        except Exception as e:
            logger.error(f"Error creating role config modal: {e}")
            return cls(guild_id)
    
    async def on_submit(self, interaction: discord.Interaction):
        """Handle modal submission"""
        try:
            # Parse giveaway participant roles
            giveaway_roles = []
            if self.giveaway_roles_input.value.strip():
                try:
                    role_ids = [int(role_id.strip()) for role_id in self.giveaway_roles_input.value.split(',') if role_id.strip()]
                    # Validate roles exist in guild
                    for role_id in role_ids:
                        role = interaction.guild.get_role(role_id)
                        if role:
                            giveaway_roles.append(role_id)
                        else:
                            logger.warning(f"Role {role_id} not found in guild {interaction.guild.id}")
                except ValueError:
                    embed = create_error_embed(
                        "Invalid Role IDs",
                        "Please enter valid role IDs separated by commas."
                    )
                    await interaction.response.send_message(embed=embed, ephemeral=True)
                    return
            
            # Parse bot admin roles
            admin_roles = []
            if self.admin_roles_input.value.strip():
                try:
                    role_ids = [int(role_id.strip()) for role_id in self.admin_roles_input.value.split(',') if role_id.strip()]
                    # Validate roles exist in guild
                    for role_id in role_ids:
                        role = interaction.guild.get_role(role_id)
                        if role:
                            admin_roles.append(role_id)
                        else:
                            logger.warning(f"Role {role_id} not found in guild {interaction.guild.id}")
                except ValueError:
                    embed = create_error_embed(
                        "Invalid Role IDs",
                        "Please enter valid role IDs separated by commas."
                    )
                    await interaction.response.send_message(embed=embed, ephemeral=True)
                    return
            
            # Parse authorization requirement
            require_auth_str = self.require_auth_input.value.strip().lower()
            if require_auth_str in ['true', '1', 'yes', 'on']:
                require_auth = True
            elif require_auth_str in ['false', '0', 'no', 'off']:
                require_auth = False
            else:
                embed = create_error_embed(
                    "Invalid Authorization Setting",
                    "Please enter 'true' or 'false' for the authorization requirement."
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
                return
            
            # Update database
            success = True
            success &= await db_manager.update_giveaway_participant_roles(self.guild_id, giveaway_roles)
            success &= await db_manager.update_bot_admin_roles(self.guild_id, admin_roles)
            success &= await db_manager.update_require_authorization(self.guild_id, require_auth)
            
            if success:
                # Build success message
                giveaway_role_mentions = []
                admin_role_mentions = []
                
                for role_id in giveaway_roles:
                    role = interaction.guild.get_role(role_id)
                    if role:
                        giveaway_role_mentions.append(role.mention)
                
                for role_id in admin_roles:
                    role = interaction.guild.get_role(role_id)
                    if role:
                        admin_role_mentions.append(role.mention)
                
                description = "**🎉 Giveaway Participant Roles:**\n"
                if giveaway_role_mentions:
                    description += f"• {', '.join(giveaway_role_mentions)}\n"
                else:
                    description += "• No role restrictions (all members can participate)\n"
                
                description += "\n**🔧 Bot Admin Roles:**\n"
                if admin_role_mentions:
                    description += f"• {', '.join(admin_role_mentions)}\n"
                else:
                    description += "• Only server owner has admin access\n"
                
                description += f"\n**🔐 Authorization Required:** {'Yes' if require_auth else 'No'}\n"
                
                if require_auth:
                    description += "\n*Users must complete `/authorize` in addition to having required roles.*"
                
                embed = create_success_embed(
                    "✅ Role Permissions Updated",
                    description
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
            else:
                embed = create_error_embed(
                    "Configuration Failed",
                    "Failed to update role configuration. Please try again."
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
                
        except Exception as e:
            logger.error(f"Error in role config modal submission: {e}")
            embed = create_error_embed(
                "Configuration Error",
                "An error occurred while updating the configuration. Please try again."
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
