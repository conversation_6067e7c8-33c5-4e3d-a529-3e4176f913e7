"""
Simple integration tests to verify core Discord bot functionality.
Tests basic imports and module availability without complex mocking.
"""
import asyncio
import sys
import os
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from test_base import AsyncTestCase, print_test_header, print_test_result

class SimpleIntegrationTests(AsyncTestCase):
    """Simple integration tests for core bot functionality"""
    
    def __init__(self):
        super().__init__()
    
    async def test_core_imports(self):
        """Test that all core modules can be imported"""
        print_test_header("Core Module Imports")
        
        try:
            # Test core imports
            from core.bot import MemoryOptimizedBot
            from core.config import config, IntentsConfig
            from core.logging import logger
            from core.exceptions import DatabaseError
            
            print_test_result("Core Imports", True, "All core modules imported successfully")
            return True
            
        except Exception as e:
            print_test_result("Core Imports", False, f"Import error: {str(e)}")
            return False
    
    async def test_database_imports(self):
        """Test database module imports"""
        print_test_header("Database Module Imports")
        
        try:
            from utils.database import DatabaseManager
            from utils.database import db_manager
            
            print_test_result("Database Imports", True, "Database modules imported successfully")
            return True
            
        except Exception as e:
            print_test_result("Database Imports", False, f"Import error: {str(e)}")
            return False
    
    async def test_oauth_imports(self):
        """Test OAuth module imports"""
        print_test_header("OAuth Module Imports")
        
        try:
            from utils.oauth_advanced import AdvancedTokenManager
            
            print_test_result("OAuth Imports", True, "OAuth modules imported successfully")
            return True
            
        except Exception as e:
            print_test_result("OAuth Imports", False, f"Import error: {str(e)}")
            return False
    
    async def test_embed_imports(self):
        """Test embed builder imports"""
        print_test_header("Embed Builder Imports")
        
        try:
            from embeds.builders import (
                create_success_embed, create_error_embed, create_info_embed
            )
            
            print_test_result("Embed Imports", True, "Embed builders imported successfully")
            return True
            
        except Exception as e:
            print_test_result("Embed Imports", False, f"Import error: {str(e)}")
            return False
    
    async def test_event_imports(self):
        """Test event handler imports"""
        print_test_header("Event Handler Imports")

        try:
            from events.member_events import setup_member_events

            print_test_result("Event Imports", True, "Event setup function imported successfully")
            return True

        except Exception as e:
            print_test_result("Event Imports", False, f"Import error: {str(e)}")
            return False
    
    async def test_command_imports(self):
        """Test command module imports"""
        print_test_header("Command Module Imports")
        
        try:
            from commands.slash_commands import setup_slash_commands
            
            print_test_result("Command Imports", True, "Command modules imported successfully")
            return True
            
        except Exception as e:
            print_test_result("Command Imports", False, f"Import error: {str(e)}")
            return False
    
    async def test_discord_py_compatibility(self):
        """Test discord.py compatibility and version"""
        print_test_header("Discord.py Compatibility")
        
        try:
            import discord
            from discord.ext import commands
            
            # Check version
            version = discord.__version__
            major, minor = map(int, version.split('.')[:2])
            
            if major >= 2:
                print_test_result("Discord.py Version", True, f"Version {version} (v2.0+ compatible)")
                return True
            else:
                print_test_result("Discord.py Version", False, f"Version {version} (requires v2.0+)")
                return False
                
        except Exception as e:
            print_test_result("Discord.py Compatibility", False, f"Error: {str(e)}")
            return False
    
    async def test_basic_embed_creation(self):
        """Test basic embed creation functionality"""
        print_test_header("Basic Embed Creation")
        
        try:
            from embeds.builders import create_success_embed
            import discord
            
            # Create a simple embed
            embed = create_success_embed("Test message")
            
            # Verify it's a Discord embed
            if isinstance(embed, discord.Embed):
                print_test_result("Embed Creation", True, "Successfully created Discord embed")
                return True
            else:
                print_test_result("Embed Creation", False, "Created object is not a Discord embed")
                return False
                
        except Exception as e:
            print_test_result("Embed Creation", False, f"Error: {str(e)}")
            return False

async def run_simple_integration_tests():
    """Run all simple integration tests"""
    print("=" * 80)
    print("🧪 SIMPLE INTEGRATION TESTS")
    print("=" * 80)
    print("Testing core Discord bot functionality without complex mocking")
    print("-" * 80)
    
    test_suite = SimpleIntegrationTests()
    
    # List of tests to run
    tests = [
        test_suite.test_core_imports,
        test_suite.test_database_imports,
        test_suite.test_oauth_imports,
        test_suite.test_embed_imports,
        test_suite.test_event_imports,
        test_suite.test_command_imports,
        test_suite.test_discord_py_compatibility,
        test_suite.test_basic_embed_creation
    ]
    
    results = []
    
    for test in tests:
        try:
            result = await test_suite.run_async_test(test)
            results.append(result)
        except Exception as e:
            print_test_result(test.__name__, False, f"Test execution error: {str(e)}")
            results.append(False)
    
    # Print summary
    passed = sum(results)
    total = len(results)
    
    print("\n" + "=" * 80)
    print("📊 SIMPLE INTEGRATION TEST SUMMARY")
    print("=" * 80)
    print(f"Passed: {passed}/{total}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("✅ All integration tests passed!")
        print("🎉 Core bot functionality is working correctly")
    else:
        print("⚠️  Some integration tests failed")
        print("🔧 Check the failed imports and fix any missing dependencies")
    
    print("=" * 80)
    
    return passed == total

if __name__ == "__main__":
    # Set up proper event loop for Windows
    if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    # Run tests
    success = asyncio.run(run_simple_integration_tests())
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)
