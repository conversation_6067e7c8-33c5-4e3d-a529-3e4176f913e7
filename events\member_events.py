"""
Member event handlers for ghost welcome pings and milestone tracking.
Handles member join/leave events with customizable features.
"""
import discord
from discord.ext import commands
import asyncio
from datetime import datetime

from core.logging import logger
from embeds.builders import create_success_embed, GiveawayEmbed
from utils.database import db_manager

def setup_member_events(bot: commands.Bot):
    """Setup member-related event handlers"""
    
    @bot.event
    async def on_reaction_add(reaction, user):
        """Handle reactions for giveaway entries"""
        if user.bot:
            return
            
        # Check if it's a giveaway reaction
        if str(reaction.emoji) == "🎉":
            from utils.database import db_manager
            from utils.permissions import permission_manager
            from embeds.builders import create_success_embed, create_error_embed
            from config.messages import GiveawayMessages
            
            # Check if this message is a giveaway
            giveaway = await db_manager.get_giveaway(reaction.message.id)
            if giveaway:
                try:
                    # Check if user can participate in giveaways
                    if not await permission_manager.can_participate_in_giveaways(user):
                        # Remove the reaction since they can't participate
                        try:
                            await reaction.remove(user)
                        except:
                            pass
                        
                        # Send ephemeral warning via DM
                        try:
                            embed = create_error_embed(
                                GiveawayMessages.WARNING_TITLE,
                                GiveawayMessages.get_warning_message(is_quick=False)
                            )
                            await user.send(embed=embed)
                        except:
                            pass
                        return

                    # Calculate bonus entries for the user
                    bonus_info = await permission_manager.get_user_bonus_info(user)
                    total_entries = bonus_info['total_entries']
                    
                    # Add participant to giveaway with bonus entries
                    success = await db_manager.add_giveaway_participant(
                        reaction.message.id,
                        user.id,
                        total_entries
                    )

                    if success:
                        # Send success DM
                        try:
                            if total_entries > 1:
                                bonus_text = f"\n\n{GiveawayMessages.BONUS_ENTRY_DETAILS.format(entries=total_entries)}"
                                if bonus_info['highest_role']:
                                    bonus_text += f"\nThanks to your **{bonus_info['highest_role'].name}** role!"
                            else:
                                bonus_text = f"\n\n{GiveawayMessages.NO_BONUS_MESSAGE}"

                            embed = create_success_embed(
                                "✅ Entered Giveaway!",
                                f"You've successfully entered the giveaway for **{giveaway['title']}**!{bonus_text}"
                            )
                            await user.send(embed=embed)
                        except:
                            pass
                    else:
                        # Already entered - remove reaction
                        try:
                            await reaction.remove(user)
                        except:
                            pass
                            
                except Exception as e:
                    logger.error(f"Error handling giveaway reaction: {e}")

    @bot.event
    async def on_reaction_remove(reaction, user):
        """Handle reaction removal for giveaways"""
        if user.bot:
            return
            
        # Check if it's a giveaway reaction
        if str(reaction.emoji) == "🎉":
            from utils.database import db_manager
            
            # Check if this message is a giveaway
            giveaway = await db_manager.get_giveaway(reaction.message.id)
            if giveaway:
                # Remove participant from giveaway
                await db_manager.remove_giveaway_participant(reaction.message.id, user.id)
    
    @bot.event
    async def on_member_join(member):
        """Handle member join events"""
        try:
            # Get guild configuration
            config = await db_manager.get_guild_config(member.guild.id)
            if not config:
                return
            
            # Ghost welcome ping
            if config['welcome_channel']:
                await handle_ghost_welcome_ping(member, config['welcome_channel'])
            
            # Check for milestone giveaway
            if config['milestone_channel'] and config['milestone_count']:
                await check_milestone_giveaway(member, config)
                
        except Exception as e:
            logger.error(f"Error in member join event: {e}")
    
    @bot.event
    async def on_member_remove(member):
        """Handle member leave events"""
        try:
            # Update member count tracking
            config = await db_manager.get_guild_config(member.guild.id)
            if config:
                await db_manager.update_guild_config(
                    member.guild.id,
                    last_member_count=member.guild.member_count
                )

        except Exception as e:
            logger.error(f"Error in member remove event: {e}")

    @bot.event
    async def on_guild_join(guild):
        """Handle bot joining new guilds"""
        try:
            logger.info(f"Bot joined new guild: {guild.name} (ID: {guild.id}, Members: {guild.member_count})")

            # Initialize guild configuration with default values
            success = await db_manager.update_guild_config(
                guild.id,
                last_member_count=guild.member_count,
                milestone_count=500,  # Default milestone
                require_authorization=1,
                giveaway_participant_roles='[]',
                bot_admin_roles='[]',
                role_bonus_multipliers='{}',
                partner_servers='[]'
            )

            if success:
                logger.info(f"Initialized configuration for guild {guild.name}")
            else:
                logger.warning(f"Failed to initialize configuration for guild {guild.name}")

            # Try to send a welcome message to the system channel or first available text channel
            welcome_channel = guild.system_channel
            if not welcome_channel:
                # Find the first text channel the bot can send messages to
                for channel in guild.text_channels:
                    if channel.permissions_for(guild.me).send_messages:
                        welcome_channel = channel
                        break

            if welcome_channel:
                try:
                    from embeds.builders import create_info_embed
                    embed = create_info_embed(
                        "🎉 Thanks for adding me!",
                        "Hi there! I'm your new giveaway bot. Here's how to get started:\n\n"
                        "**🔧 Setup Commands:**\n"
                        "• `/config` - Configure channels and settings\n"
                        "• `/setup` - Quick setup wizard\n\n"
                        "**🎁 Giveaway Commands:**\n"
                        "• `/giveaway create` - Create a new giveaway\n"
                        "• `/giveaway quick` - Quick 5-minute giveaway\n\n"
                        "**🔐 Authorization:**\n"
                        "• `/authorize` - Enable premium features\n"
                        "• Users need to authorize to participate in giveaways\n\n"
                        "**💡 Need Help?**\n"
                        "• `/help` - Show all available commands\n"
                        "• Make sure I have proper permissions in your channels!"
                    )
                    await welcome_channel.send(embed=embed)
                    logger.info(f"Sent welcome message to {guild.name}")
                except Exception as e:
                    logger.warning(f"Could not send welcome message to {guild.name}: {e}")

        except Exception as e:
            logger.error(f"Error in guild join event for {guild.name}: {e}")

    @bot.event
    async def on_guild_remove(guild):
        """Handle bot leaving guilds"""
        try:
            logger.info(f"Bot removed from guild: {guild.name} (ID: {guild.id})")

            # Optionally clean up guild data (commented out to preserve data)
            # await db_manager.cleanup_guild_data(guild.id)

        except Exception as e:
            logger.error(f"Error in guild remove event for {guild.name}: {e}")

    async def handle_ghost_welcome_ping(member, welcome_channel_id):
        """Handle ghost welcome ping that deletes after 1 second"""
        try:
            channel = member.guild.get_channel(welcome_channel_id)
            if not channel:
                logger.warning(f"Welcome channel {welcome_channel_id} not found in {member.guild}")
                return
            
            # Send welcome notification
            welcome_message = await channel.send(f"👋 Welcome to the community {member.mention}!")

            # Delete after brief delay
            await asyncio.sleep(1.2)
            try:
                await welcome_message.delete()
                logger.debug(f"Ghost welcome ping sent for {member} in {member.guild}")
            except discord.NotFound:
                pass  # Message already deleted
            except discord.Forbidden:
                logger.warning(f"Cannot delete welcome message in {channel} - missing permissions")
                
        except discord.Forbidden:
            logger.warning(f"Cannot send welcome message in channel {welcome_channel_id} - missing permissions")
        except Exception as e:
            logger.error(f"Error in ghost welcome ping: {e}")
    
    async def check_milestone_giveaway(member, config):
        """Check if member count reached milestone for automatic giveaway"""
        try:
            current_count = member.guild.member_count
            milestone_count = config['milestone_count']
            last_count = config['last_member_count']
            
            # Check if we crossed a milestone
            if current_count >= milestone_count and last_count < milestone_count:
                # We hit a milestone!
                milestone_channel = member.guild.get_channel(config['milestone_channel'])
                if not milestone_channel:
                    return
                
                # Create milestone giveaway
                prizes = [
                    f"🎊 {milestone_count} Members Celebration Prize!",
                    f"🎉 Milestone Reward - {milestone_count} Members!",
                    f"🏆 Community Growth Prize - {milestone_count} Strong!",
                    f"⭐ Special Milestone Gift - {milestone_count} Members!",
                    f"💎 {milestone_count} Members Reward!"
                ]
                
                import random
                prize = random.choice(prizes)
                
                # Create giveaway embed
                embed = GiveawayEmbed(
                    f"🎊 MILESTONE GIVEAWAY! 🎊",
                    f"🎉 **We reached {milestone_count} members!** 🎉\n\n"
                    f"🏆 **Prize:** {prize}\n"
                    f"👥 **Winners:** 1\n"
                    f"⏰ **Duration:** 24 hours\n"
                    f"🎯 **Triggered by:** {member.mention} joining!\n\n"
                    f"**How to Enter:**\n"
                    f"1️⃣ Click the button below to enter\n"
                    f"2️⃣ Authorization required (use `/authorize`)\n"
                    f"3️⃣ Celebrate our community growth!\n\n"
                    f"⚠️ **Note:** Authorization unlocks giveaway opportunities"
                ).set_footer(f"Milestone: {milestone_count} members reached!").build()
                
                # Send milestone giveaway
                message = await milestone_channel.send(embed=embed)
                await message.add_reaction("🎉")
                
                # Save to database (24 hour duration)
                from datetime import timedelta
                end_time = datetime.now() + timedelta(hours=24)
                
                success = await db_manager.create_giveaway(
                    message.id, member.guild.id, milestone_channel.id,
                    prize, embed.description, end_time, 1, member.guild.me.id
                )
                
                if success:
                    logger.info(f"Milestone giveaway created for {milestone_count} members in {member.guild}")
                
                # Update milestone count for next milestone
                next_milestone = milestone_count + 500  # Next milestone in 500 members
                await db_manager.update_guild_config(
                    member.guild.id,
                    milestone_count=next_milestone,
                    last_member_count=current_count
                )
                
                logger.info(f"Milestone reached: {milestone_count} members in {member.guild}")
            
            else:
                # Just update the member count
                await db_manager.update_guild_config(
                    member.guild.id,
                    last_member_count=current_count
                )
                
        except Exception as e:
            logger.error(f"Error checking milestone giveaway: {e}")
    
    logger.info("Member events loaded")
