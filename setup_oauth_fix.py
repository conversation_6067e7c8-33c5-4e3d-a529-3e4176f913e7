#!/usr/bin/env python3
"""
Discord OAuth2 Authentication Fix
Based on working examples from the web
"""

import os
import asyncio
import aiohttp
from datetime import datetime

# Load environment variables from .env file (CRITICAL FIX)
from dotenv import load_dotenv
load_dotenv()

def create_env_template():
    """Create a proper .env file template"""
    env_content = """# Discord Bot Configuration
DISCORD_TOKEN=your_bot_token_here

# Discord OAuth2 Configuration (REQUIRED for authentication)
DISCORD_CLIENT_SECRET=your_discord_client_secret_here
REDIRECT_URI=http://localhost:8080/callback

# OAuth Server Configuration
OAUTH_HOST=localhost
OAUTH_PORT=8080

# Database Configuration
DATABASE_URL=bot_data.db

# Logging
LOG_LEVEL=INFO
"""
    
    if not os.path.exists('.env'):
        with open('.env', 'w') as f:
            f.write(env_content)
        print("✅ Created .env file template")
    else:
        print("⚠️  .env file already exists")
    
    print("""
🔧 CRITICAL: You must update .env with your actual values:

1. Get your Discord Client Secret:
   - Go to https://discord.com/developers/applications
   - Select your bot application
   - Go to "OAuth2" → "General"
   - Copy the "CLIENT SECRET" (NOT the Client ID)
   - Paste it in .env as DISCORD_CLIENT_SECRET=your_actual_secret

2. Verify your redirect URI:
   - In Discord Developer Portal → OAuth2 → Redirects
   - Add: http://localhost:8080/callback
   - Make sure it matches exactly!

3. Your CLIENT_ID is already set in config/messages.py: 1398041068703977613
""")

async def test_discord_api():
    """Test Discord API connectivity"""
    print("\n🧪 Testing Discord API connectivity...")
    
    try:
        async with aiohttp.ClientSession() as session:
            # Test with invalid token (should return 401)
            async with session.get(
                'https://discord.com/api/users/@me', 
                headers={'Authorization': 'Bearer invalid_token'}
            ) as resp:
                if resp.status == 401:
                    print("✅ Discord API is accessible (returned 401 for invalid token)")
                    return True
                else:
                    print(f"⚠️  Unexpected response: {resp.status}")
                    return False
    except Exception as e:
        print(f"❌ Error connecting to Discord API: {e}")
        return False

def check_current_config():
    """Check current OAuth configuration"""
    print("🔍 Current OAuth Configuration:")
    
    # Check environment variables
    client_secret = os.getenv('DISCORD_CLIENT_SECRET')
    redirect_uri = os.getenv('REDIRECT_URI')
    
    print(f"  DISCORD_CLIENT_SECRET: {'✅ SET' if client_secret else '❌ NOT SET'}")
    print(f"  REDIRECT_URI: {redirect_uri or '❌ NOT SET'}")
    
    # Check if values look valid
    if client_secret:
        if client_secret.startswith('your_') or 'here' in client_secret.lower():
            print("  ⚠️  CLIENT_SECRET appears to be placeholder - update with real value!")
            return False
        else:
            print("  ✅ CLIENT_SECRET appears to be set with real value")
    
    # Check config file
    try:
        with open('config/messages.py', 'r') as f:
            content = f.read()
            if '1398041068703977613' in content:
                print("  ✅ CLIENT_ID found in config/messages.py")
            else:
                print("  ⚠️  CLIENT_ID not found in config")
    except Exception as e:
        print(f"  ❌ Error reading config: {e}")
    
    return bool(client_secret and redirect_uri)

async def main():
    """Main setup and diagnostic function"""
    print("🚀 Discord OAuth2 Authentication Fix Tool")
    print("=" * 50)
    
    # Create .env template
    create_env_template()
    
    # Check current configuration
    config_ok = check_current_config()
    
    # Test Discord API
    api_ok = await test_discord_api()
    
    print("\n📋 Summary:")
    print(f"  Configuration: {'✅ OK' if config_ok else '❌ NEEDS SETUP'}")
    print(f"  Discord API:   {'✅ OK' if api_ok else '❌ CONNECTION ISSUE'}")
    
    if not config_ok:
        print("""
⚠️  NEXT STEPS TO FIX OAUTH:

1. Edit your .env file with real values
2. Get Discord Client Secret from Developer Portal
3. Verify redirect URI in Discord settings
4. Restart your bot

Then try /authorize command again!
""")
    else:
        print("\n✅ Configuration looks good! OAuth should work now.")

if __name__ == "__main__":
    asyncio.run(main())