# 🤖 Guía de Configuración del Bot de Discord

## 📋 Requisitos Previos

- Python 3.8 o superior
- Una aplicación de Discord creada en el [Portal de Desarrolladores](https://discord.com/developers/applications)
- Permisos de administrador en el servidor donde quieres usar el bot

## 🚀 Configuración Inicial

### 1. Configurar la Aplicación en Discord

#### A. Crear la Aplicación
1. Ve al [Portal de Desarrolladores de Discord](https://discord.com/developers/applications)
2. Haz clic en "New Application"
3. Dale un nombre a tu aplicación
4. Guarda el **Application ID** (lo necesitarás más tarde)

#### B. Configurar el Bot
1. Ve a la pestaña "Bot"
2. Haz clic en "Add Bot"
3. **IMPORTANTE**: Activa temporalmente "Public Bot" (necesario para OAuth2)
4. Copia el **Token** del bot (man<PERSON>n<PERSON> seguro)

#### C. Habilitar Intents Privilegiados
**⚠️ CRÍTICO**: Para evitar el error `PrivilegedIntentsRequired`, debes habilitar:

1. En la pestaña "Bot", desplázate hasta "Privileged Gateway Intents"
2. Activa las siguientes opciones:
   - ✅ **Presence Intent**
   - ✅ **Server Members Intent** 
   - ✅ **Message Content Intent**
3. Guarda los cambios

#### D. Configurar OAuth2
1. Ve a la pestaña "OAuth2" → "General"
2. Copia el **Client Secret** (lo necesitarás para OAuth2)
3. Ve a "OAuth2" → "URL Generator"
4. En "Redirects", añade: `http://localhost:8080/callback`
5. **IMPORTANTE**: Ve a "Installation" y marca "Guild Install" temporalmente

### 2. Configurar Variables de Entorno

1. Copia el archivo `.env.example` a `.env`:
   ```bash
   cp .env.example .env
   ```

2. Edita el archivo `.env` con tus valores reales:
   ```env
   DISCORD_TOKEN=tu_token_del_bot_aquí
   APPLICATION_ID=tu_application_id_aquí
   DISCORD_CLIENT_SECRET=tu_client_secret_aquí
   REDIRECT_URI=http://localhost:8080/callback
   ```

### 3. Instalar Dependencias

```bash
pip install -r requirements.txt
```

### 4. Ejecutar el Bot

```bash
python main.py
```

## 🔧 Solución al Error "unsupported_response_type"

### ❌ Problema
Si recibes el error `unsupported_response_type` al intentar autorizar el bot:

### ✅ Solución

#### 1. Verificar Configuración OAuth2
El bot ahora usa el flujo **Authorization Code Grant** correcto:
- ✅ `response_type=code` (NO `response_type=token`)
- ✅ `grant_type=authorization_code` para intercambio de tokens
- ✅ `Content-Type: application/x-www-form-urlencoded`

#### 2. Configuración en Discord Developer Portal
1. **Bot** → Activa temporalmente "Public Bot"
2. **OAuth2** → "Installation" → Marca "Guild Install"
3. **OAuth2** → "Redirects" → Añade `http://localhost:8080/callback`

#### 3. Proceso de Autorización
1. El bot genera una URL OAuth2 con `response_type=code`
2. El usuario autoriza y es redirigido con un `code`
3. El bot intercambia el `code` por un `access_token`
4. Usa el `access_token` para unir al usuario a servidores

## 🎯 Invitar el Bot a tu Servidor

### Método 1: URL de Invitación Básica
1. Ve a "OAuth2" → "URL Generator"
2. Selecciona scopes: `bot`
3. Selecciona permisos necesarios
4. Usa la URL generada

### Método 2: Con Funciones OAuth2 (Recomendado)
1. Usa el comando `/authorize` en el bot
2. Sigue el proceso de autorización OAuth2
3. El bot podrá unirse automáticamente a servidores

## 🛠️ Comandos Principales

- `/giveaway create` - Crear un sorteo
- `/giveaway end` - Finalizar un sorteo
- `/authorize` - Autorizar funciones avanzadas
- `/config` - Configurar el bot
- `/help` - Mostrar ayuda

## 🔍 Verificar Configuración

Ejecuta el script de prueba:
```bash
python test_bot.py
```

Esto verificará:
- ✅ Archivos del proyecto
- ✅ Variables de entorno
- ✅ Dependencias de Python
- ✅ Conexión a la base de datos

## ❗ Solución de Problemas

### Error: `PrivilegedIntentsRequired`
- **Solución**: Habilita todos los intents privilegiados en el Portal de Desarrolladores

### Error: `unsupported_response_type`
- **Solución**: Verifica que DISCORD_CLIENT_SECRET y REDIRECT_URI estén configurados
- **Solución**: Activa temporalmente "Public Bot" y "Guild Install"

### Error: `Invalid OAuth2 access token`
- **Solución**: Verifica que el token tenga el scope `guilds.join`
- **Solución**: Regenera el Client Secret si es necesario

### El bot no responde
- **Solución**: Verifica que el token sea correcto
- **Solución**: Asegúrate de que el bot tenga permisos en el servidor

### Error de base de datos
- **Solución**: Verifica que el archivo `bot_data.db` sea accesible
- **Solución**: Ejecuta `python test_bot.py` para verificar la configuración

## 📞 Soporte

Si necesitas ayuda:
1. Verifica que hayas seguido todos los pasos
2. Ejecuta `python test_bot.py` para diagnosticar problemas
3. Revisa los logs del bot para errores específicos
4. Consulta la documentación de Discord.py

## 🔒 Seguridad

- ⚠️ **NUNCA** compartas tu token del bot
- ⚠️ **NUNCA** subas el archivo `.env` a repositorios públicos
- 🔄 Regenera tokens si sospechas que han sido comprometidos
- 🔐 Usa permisos mínimos necesarios para el bot

---

¡Tu bot está listo para usar! 🎉