"""
Modern slash commands with comprehensive dashboard system.
Implements full giveaway bot functionality with modals and interactive UI.
"""
import discord
from discord import app_commands
from discord.ext import commands
from typing import Optional, Literal
import random
import asyncio
from datetime import datetime, timedelta

from core.logging import logger
from core.config import Colors, Emojis
from embeds.builders import (
    create_success_embed, create_error_embed, create_info_embed,
    GiveawayEmbed, InfoEmbed, EmbedTemplates
)
from interactions.views.base import BaseView, ConfirmationView
from interactions.modals import (
    GiveawayModal, QuickGiveawayModal, ServerSetupModal, InviteUsersModal,
    WelcomeChannelModal, ActivityChannelModal, MilestoneChannelModal, MessageCountsModal,
    PartnerServerModal
)
from interactions.prize_selection import PrizeSelectionView
from interactions.emoji_config import EmojiConfigModal, show_current_emojis
from utils.database import db_manager
from enum import Enum

# Old button-based giveaway views removed - using pure reaction system

# Quick giveaway now uses pure reaction system - no buttons

class DashboardPage(Enum):
    """Dashboard page types"""
    MAIN = "main"
    GIVEAWAYS = "giveaways"  # Giveaway section with sub-options
    GIVEAWAY_SELECTION = "giveaway_selection"
    CONFIGURATION = "configuration"  # Configuration section with sub-options
    PARTNER_SERVERS = "partner_servers"  # Partner server management
    SERVER_STATS = "server_stats"  # Combined auth stats and bot info
    VIEW_CONFIG = "view_config"

class ComprehensiveDashboard(BaseView):
    """Comprehensive bot dashboard with all features and navigation"""

    def __init__(self, user_id: int):
        super().__init__(timeout=600.0, user_id=user_id)  # 10 minute timeout
        self.current_page = DashboardPage.MAIN
        self.guild_id = None  # Will be set when first used
        self._setup_main_page()

    def _setup_main_page(self):
        """Setup buttons for the main dashboard page"""
        self.clear_items()

        # Row 0 - Main sections
        self.add_item(discord.ui.Button(
            label="Giveaways",
            style=discord.ButtonStyle.primary,
            emoji="🎉",
            row=0,
            custom_id="giveaways_section"
        ))
        self.add_item(discord.ui.Button(
            label="Configuration",
            style=discord.ButtonStyle.secondary,
            emoji="⚙️",
            row=0,
            custom_id="configuration_section"
        ))
        self.add_item(discord.ui.Button(
            label="Add Members",
            style=discord.ButtonStyle.success,
            emoji="👥",
            row=0,
            custom_id="invite_users"
        ))

        # Row 1 - Information
        self.add_item(discord.ui.Button(
            label="Server Stats",
            style=discord.ButtonStyle.secondary,
            emoji="📊",
            row=1,
            custom_id="server_stats"
        ))
        self.add_item(discord.ui.Button(
            label="View Config",
            style=discord.ButtonStyle.secondary,
            emoji="🔍",
            row=1,
            custom_id="view_config"
        ))

        # Set up callbacks
        for item in self.children:
            if hasattr(item, 'custom_id') and hasattr(item, 'callback'):
                item.callback = self._handle_button_click

    def _setup_giveaway_section_buttons(self):
        """Setup buttons for the giveaway section"""
        self.clear_items()

        # Giveaway options
        self.add_item(discord.ui.Button(
            label="Create Giveaway",
            style=discord.ButtonStyle.primary,
            emoji="🎁",
            row=0,
            custom_id="create_giveaway"
        ))
        self.add_item(discord.ui.Button(
            label="Quick Giveaway",
            style=discord.ButtonStyle.success,
            emoji="⚡",
            row=0,
            custom_id="quick_giveaway"
        ))

        # Back button
        self.add_item(discord.ui.Button(
            label="Back to Dashboard",
            style=discord.ButtonStyle.secondary,
            emoji="🔙",
            row=1,
            custom_id="back_to_main"
        ))

        # Set up callbacks
        for item in self.children:
            if hasattr(item, 'custom_id') and hasattr(item, 'callback'):
                item.callback = self._handle_button_click

    def _setup_configuration_section_buttons(self):
        """Setup buttons for the configuration section"""
        self.clear_items()

        # Row 0 - Channel configurations
        self.add_item(discord.ui.Button(
            label="Welcome Channel",
            style=discord.ButtonStyle.secondary,
            emoji="👋",
            row=0,
            custom_id="config_welcome"
        ))
        self.add_item(discord.ui.Button(
            label="Activity Channel",
            style=discord.ButtonStyle.secondary,
            emoji="💬",
            row=0,
            custom_id="config_activity"
        ))
        self.add_item(discord.ui.Button(
            label="Milestone Channel",
            style=discord.ButtonStyle.secondary,
            emoji="🎊",
            row=0,
            custom_id="config_milestone"
        ))

        # Row 1 - Settings
        self.add_item(discord.ui.Button(
            label="Message Counts",
            style=discord.ButtonStyle.secondary,
            emoji="📊",
            row=1,
            custom_id="config_counts"
        ))
        self.add_item(discord.ui.Button(
            label="Emoji Settings",
            style=discord.ButtonStyle.secondary,
            emoji="🎨",
            row=1,
            custom_id="configure_emojis"
        ))
        self.add_item(discord.ui.Button(
            label="Role Permissions",
            style=discord.ButtonStyle.secondary,
            emoji="🔐",
            row=1,
            custom_id="config_roles"
        ))

        # Row 2 - Bonus entries, partner servers, and contact support
        self.add_item(discord.ui.Button(
            label="Bonus Entries",
            style=discord.ButtonStyle.secondary,
            emoji="🎯",
            row=2,
            custom_id="config_bonus_entries"
        ))
        self.add_item(discord.ui.Button(
            label="Partner Servers",
            style=discord.ButtonStyle.success,
            emoji="🤝",
            row=2,
            custom_id="partner_servers"
        ))
        self.add_item(discord.ui.Button(
            label="Contact Support",
            style=discord.ButtonStyle.secondary,
            emoji="📞",
            row=2,
            custom_id="config_contact_support"
        ))

        # Row 3 - Back button
        self.add_item(discord.ui.Button(
            label="Back to Dashboard",
            style=discord.ButtonStyle.secondary,
            emoji="🔙",
            row=3,
            custom_id="back_to_main"
        ))

        # Set up callbacks
        for item in self.children:
            if hasattr(item, 'custom_id') and hasattr(item, 'callback'):
                item.callback = self._handle_button_click

    def _setup_giveaway_page(self):
        """Setup buttons for the giveaway selection page"""
        self.clear_items()

        # Back button
        back_button = discord.ui.Button(
            label="Back to Dashboard",
            style=discord.ButtonStyle.secondary,
            emoji="🔙",
            row=4,
            custom_id="back_to_main"
        )
        back_button.callback = self._back_to_main
        self.add_item(back_button)

    def _setup_sub_page(self):
        """Setup buttons for sub-pages (stats, config, bot info)"""
        self.clear_items()

        # Back button
        back_button = discord.ui.Button(
            label="Back to Dashboard",
            style=discord.ButtonStyle.secondary,
            emoji="🔙",
            row=4,
            custom_id="back_to_main"
        )
        back_button.callback = self._back_to_main
        self.add_item(back_button)

    async def _handle_button_click(self, interaction: discord.Interaction):
        """Handle button clicks and route to appropriate handlers"""
        if not self.guild_id:
            self.guild_id = interaction.guild.id

        custom_id = interaction.data.get('custom_id')

        if custom_id == "giveaways_section":
            await self._giveaways_section(interaction)
        elif custom_id == "configuration_section":
            await self._configuration_section(interaction)
        elif custom_id == "create_giveaway":
            await self._create_giveaway(interaction)
        elif custom_id == "quick_giveaway":
            await self._quick_giveaway(interaction)
        elif custom_id == "server_stats":
            await self._server_stats(interaction)
        elif custom_id == "invite_users":
            await self._invite_users(interaction)
        elif custom_id == "server_setup":
            await self._server_setup(interaction)
        elif custom_id == "view_config":
            await self._view_config(interaction)
        elif custom_id == "configure_emojis":
            await self._configure_emojis(interaction)
        elif custom_id == "config_welcome":
            await self._config_welcome_channel(interaction)
        elif custom_id == "config_activity":
            await self._config_activity_channel(interaction)
        elif custom_id == "config_milestone":
            await self._config_milestone_channel(interaction)
        elif custom_id == "config_counts":
            await self._config_counts(interaction)
        elif custom_id == "config_roles":
            await self._config_roles(interaction)
        elif custom_id == "config_bonus_entries":
            await self._config_bonus_entries(interaction)
        elif custom_id == "config_contact_support":
            await self._config_contact_support(interaction)
        elif custom_id == "partner_servers":
            await self._partner_servers_section(interaction)
        elif custom_id == "add_partner_server":
            await self._add_partner_server(interaction)
        elif custom_id == "remove_partner_server":
            await self._remove_partner_server(interaction)
        elif custom_id == "list_partner_servers":
            await self._list_partner_servers(interaction)
        elif custom_id == "back_to_main":
            await self._back_to_main(interaction)

    async def _back_to_main(self, interaction: discord.Interaction):
        """Return to main dashboard page"""
        self.current_page = DashboardPage.MAIN
        self._setup_main_page()

        embed = InfoEmbed(
            "🎛️ Bot Dashboard",
            "Access the comprehensive bot dashboard. Select an option below to proceed.\n\n"
            "**🎉 Giveaway Features:**\n"
            "• Create custom giveaways with multiple prize types\n"
            "• Quick giveaways for instant engagement\n\n"
            "**⚙️ Server Management:**\n"
            "• Configure bot settings for your server\n"
            "• Add members to partner communities\n"
            "• Customize prize emojis\n\n"
            "**📊 Information:**\n"
            "• View current server configuration\n"
            "• Check server and bot statistics"
        ).build()

        # Use followup for ephemeral messages
        if interaction.response.is_done():
            await interaction.followup.edit_message(interaction.message.id, embed=embed, view=self)
        else:
            await interaction.response.edit_message(embed=embed, view=self)

    async def _create_giveaway(self, interaction: discord.Interaction):
        """Open prize selection for giveaway creation"""
        # Allow server owner, admins, or users with manage messages
        if not (interaction.user.id == interaction.guild.owner_id or
                interaction.user.guild_permissions.administrator or
                interaction.user.guild_permissions.manage_messages):
            embed = create_error_embed(
                "Missing Permissions",
                "You need `Manage Messages` permission or higher to create giveaways."
            )
            # Use followup for ephemeral messages
            if interaction.response.is_done():
                await interaction.followup.edit_message(interaction.message.id, embed=embed, view=self)
            else:
                await interaction.response.edit_message(embed=embed, view=self)
            return

        try:
            # Get configured emojis for display in the embed
            from interactions.emoji_config import EmojiManager
            emojis = await EmojiManager.get_guild_emojis(interaction.guild.id)

            embed = InfoEmbed(
                "🎉 Select Prize Type",
                f"Choose what type of prize you want to give away:\n\n"
                f"{emojis['nitro']} **Discord Nitro** - Various Nitro subscriptions\n"
                f"{emojis['robux']} **Robux** - Roblox currency giveaways\n"
                f"{emojis['garden']} **Grow A Garden** - Pets or fruits with quantities\n"
                f"{emojis['custom']} **Custom Prize** - Anything else you want to give away\n\n"
                f"*Click a button below to configure your giveaway* ✨"
            ).build()

            # Update page state and setup giveaway buttons
            self.current_page = DashboardPage.GIVEAWAY_SELECTION
            await self._setup_giveaway_selection_buttons(emojis)

            # Use followup for ephemeral messages
            if interaction.response.is_done():
                await interaction.followup.edit_message(interaction.message.id, embed=embed, view=self)
            else:
                await interaction.response.edit_message(embed=embed, view=self)

        except Exception as e:
            logger.error(f"Error creating giveaway interface: {e}")
            # Fallback with default emojis
            embed = InfoEmbed(
                "🎉 Select Prize Type",
                f"Choose what type of prize you want to give away:\n\n"
                f"💎 **Discord Nitro** - Various Nitro subscriptions\n"
                f"🔶 **Robux** - Roblox currency giveaways\n"
                f"🌳 **Grow A Garden** - Pets or fruits with quantities\n"
                f"🎁 **Custom Prize** - Anything else you want to give away\n\n"
                f"*Click a button below to configure your giveaway* ✨"
            ).build()

            # Setup with default emojis
            default_emojis = {'nitro': '💎', 'robux': '🔶', 'garden': '🌳', 'custom': '🎁'}
            self.current_page = DashboardPage.GIVEAWAY_SELECTION
            await self._setup_giveaway_selection_buttons(default_emojis)

            # Use followup for ephemeral messages
            if interaction.response.is_done():
                await interaction.followup.edit_message(interaction.message.id, embed=embed, view=self)
            else:
                await interaction.response.edit_message(embed=embed, view=self)

    async def _setup_giveaway_selection_buttons(self, emojis):
        """Setup buttons for giveaway prize selection"""
        self.clear_items()

        # Prize type buttons
        nitro_button = discord.ui.Button(
            label="Discord Nitro",
            style=discord.ButtonStyle.primary,
            emoji=emojis['nitro'],
            row=0,
            custom_id="nitro_prize"
        )
        nitro_button.callback = self._nitro_prize

        robux_button = discord.ui.Button(
            label="Robux",
            style=discord.ButtonStyle.success,
            emoji=emojis['robux'],
            row=0,
            custom_id="robux_prize"
        )
        robux_button.callback = self._robux_prize

        garden_button = discord.ui.Button(
            label="Grow A Garden Item",
            style=discord.ButtonStyle.secondary,
            emoji=emojis['garden'],
            row=0,
            custom_id="garden_prize"
        )
        garden_button.callback = self._garden_prize

        custom_button = discord.ui.Button(
            label="Custom Prize",
            style=discord.ButtonStyle.secondary,
            emoji=emojis['custom'],
            row=1,
            custom_id="custom_prize"
        )
        custom_button.callback = self._custom_prize

        # Back button
        back_button = discord.ui.Button(
            label="← Back to Dashboard",
            style=discord.ButtonStyle.secondary,
            emoji="🔙",
            row=2,
            custom_id="back_to_main"
        )
        back_button.callback = self._back_to_main

        # Add all buttons
        self.add_item(nitro_button)
        self.add_item(robux_button)
        self.add_item(garden_button)
        self.add_item(custom_button)
        self.add_item(back_button)

    async def _nitro_prize(self, interaction: discord.Interaction):
        """Handle Nitro prize selection"""
        from interactions.prize_selection import NitroPrizeModal
        modal = NitroPrizeModal(self.guild_id)
        await interaction.response.send_modal(modal)

    async def _robux_prize(self, interaction: discord.Interaction):
        """Handle Robux prize selection"""
        from interactions.prize_selection import RobuxPrizeModal
        modal = RobuxPrizeModal(self.guild_id)
        await interaction.response.send_modal(modal)

    async def _garden_prize(self, interaction: discord.Interaction):
        """Handle Garden prize selection"""
        from interactions.prize_selection import GardenPrizeModal
        modal = GardenPrizeModal(self.guild_id)
        await interaction.response.send_modal(modal)

    async def _custom_prize(self, interaction: discord.Interaction):
        """Handle Custom prize selection"""
        from interactions.prize_selection import CustomPrizeModal
        modal = CustomPrizeModal(self.guild_id)
        await interaction.response.send_modal(modal)

    async def _quick_giveaway(self, interaction: discord.Interaction):
        """Open quick giveaway modal"""
        # Allow server owner, admins, or users with manage messages
        if not (interaction.user.id == interaction.guild.owner_id or
                interaction.user.guild_permissions.administrator or
                interaction.user.guild_permissions.manage_messages):
            embed = create_error_embed(
                "Missing Permissions",
                "You need `Manage Messages` permission or higher to create giveaways."
            )
            # Use followup for ephemeral messages
            if interaction.response.is_done():
                await interaction.followup.edit_message(interaction.message.id, embed=embed, view=self)
            else:
                await interaction.response.edit_message(embed=embed, view=self)
            return

        modal = QuickGiveawayModal()
        await interaction.response.send_modal(modal)

    async def _server_stats(self, interaction: discord.Interaction):
        """Show combined server and bot statistics"""
        # Allow server owner, admins, or users with manage messages
        if not (interaction.user.id == interaction.guild.owner_id or
                interaction.user.guild_permissions.administrator or
                interaction.user.guild_permissions.manage_messages):
            embed = create_error_embed(
                "Missing Permissions",
                "You need `Manage Messages` permission or higher to view server stats."
            )
            # Use followup instead of edit_message for ephemeral messages
            if interaction.response.is_done():
                await interaction.followup.edit_message(interaction.message.id, embed=embed, view=self)
            else:
                await interaction.response.edit_message(embed=embed, view=self)
            return

        try:
            from core.bot import bot

            # Get authorized member count
            count = await db_manager.get_authorized_users_count()

            # Get bot stats
            uptime = bot.get_uptime()
            uptime_str = f"{uptime.days}d {uptime.seconds//3600}h {(uptime.seconds//60)%60}m"

            # Calculate latency
            latency = round(bot.latency * 1000)

            embed = InfoEmbed(
                "📊 Server & Bot Statistics",
                f"**🏠 Server Information:**\n"
                f"• **Server Members:** {interaction.guild.member_count:,}\n"
                f"• **Authorized Members:** {count:,}\n"
                f"• **Authorization Rate:** {(count/interaction.guild.member_count*100):.1f}%\n\n"
                f"**🤖 Bot Information:**\n"
                f"• **Uptime:** {uptime_str}\n"
                f"• **Latency:** {latency}ms\n"
                f"• **Servers:** {len(bot.guilds):,}\n"
                f"• **Memory Usage:** Optimized\n\n"
                f"**💎 Member Benefits:**\n"
                f"• Participation in all giveaways\n"
                f"• Joins to partner communities\n"
                f"• Pings for special events + channel access"
            ).set_thumbnail(bot.user.display_avatar.url).build()

            # Update page state and setup back button
            self.current_page = DashboardPage.SERVER_STATS
            self._setup_sub_page()

            # Use followup for ephemeral messages
            if interaction.response.is_done():
                await interaction.followup.edit_message(interaction.message.id, embed=embed, view=self)
            else:
                await interaction.response.edit_message(embed=embed, view=self)

        except Exception as e:
            logger.error(f"Error showing server stats: {e}")
            embed = create_error_embed(
                "Error",
                "Failed to load server statistics. Please try again."
            )
            if interaction.response.is_done():
                await interaction.followup.edit_message(interaction.message.id, embed=embed, view=self)
            else:
                await interaction.response.edit_message(embed=embed, view=self)

    async def _invite_users(self, interaction: discord.Interaction):
        """Show add members modal"""
        # Allow server owner or admins only (this is sensitive)
        if not (interaction.user.id == interaction.guild.owner_id or
                interaction.user.guild_permissions.administrator):
            embed = create_error_embed(
                "Missing Permissions",
                "You need `Administrator` permission to add users to other servers."
            )
            # Use followup for ephemeral messages
            if interaction.response.is_done():
                await interaction.followup.edit_message(interaction.message.id, embed=embed, view=self)
            else:
                await interaction.response.edit_message(embed=embed, view=self)
            return

        modal = InviteUsersModal()
        await interaction.response.send_modal(modal)

    async def _server_setup(self, interaction: discord.Interaction):
        """Open server configuration modal"""
        # Allow server owner or admins only
        if not (interaction.user.id == interaction.guild.owner_id or
                interaction.user.guild_permissions.administrator):
            embed = create_error_embed(
                "Missing Permissions",
                "You need `Administrator` permission to configure the server."
            )
            # Use followup for ephemeral messages
            if interaction.response.is_done():
                await interaction.followup.edit_message(interaction.message.id, embed=embed, view=self)
            else:
                await interaction.response.edit_message(embed=embed, view=self)
            return

        modal = ServerSetupModal()
        await interaction.response.send_modal(modal)

    async def _configure_emojis(self, interaction: discord.Interaction):
        """Configure prize emojis"""
        # Allow server owner or admins only
        if not (interaction.user.id == interaction.guild.owner_id or
                interaction.user.guild_permissions.administrator):
            embed = create_error_embed(
                "Missing Permissions",
                "You need `Administrator` permission to configure emojis."
            )
            # Use followup for ephemeral messages
            if interaction.response.is_done():
                await interaction.followup.edit_message(interaction.message.id, embed=embed, view=self)
            else:
                await interaction.response.edit_message(embed=embed, view=self)
            return

        modal = await EmojiConfigModal.create(interaction.guild.id)
        await interaction.response.send_modal(modal)

    async def _giveaways_section(self, interaction: discord.Interaction):
        """Show giveaway options section"""
        embed = InfoEmbed(
            "🎉 Giveaway Management",
            "Choose your giveaway option:\n\n"
            "**🎁 Create Giveaway** - Full customizable giveaway with multiple prize types\n"
            "**⚡ Quick Giveaway** - Fast giveaway for first authorized member to react\n\n"
            "*Select an option below to get started* ✨"
        ).build()

        # Update page state and setup giveaway buttons
        self.current_page = DashboardPage.GIVEAWAYS
        self._setup_giveaway_section_buttons()

        # Use followup for ephemeral messages
        if interaction.response.is_done():
            await interaction.followup.edit_message(interaction.message.id, embed=embed, view=self)
        else:
            await interaction.response.edit_message(embed=embed, view=self)

    async def _configuration_section(self, interaction: discord.Interaction):
        """Show configuration options section"""
        embed = InfoEmbed(
            "⚙️ Server Configuration",
            "Configure your server settings:\n\n"
            "**👋 Welcome Channel** - Set channel for member welcome notifications\n"
            "**💬 Activity Channel** - Configure activity-based giveaways\n"
            "**🎊 Milestone Channel** - Set member milestone celebrations\n"
            "**📊 Message Counts** - Configure activity and milestone thresholds\n"
            "**🎨 Emoji Settings** - Customize prize emojis\n"
            "**🔐 Role Permissions** - Configure who can participate in giveaways and access bot admin features\n\n"
            "*Choose what you want to configure* ⚙️"
        ).build()

        # Update page state and setup configuration buttons
        self.current_page = DashboardPage.CONFIGURATION
        self._setup_configuration_section_buttons()

        # Use followup for ephemeral messages
        if interaction.response.is_done():
            await interaction.followup.edit_message(interaction.message.id, embed=embed, view=self)
        else:
            await interaction.response.edit_message(embed=embed, view=self)

    async def _config_welcome_channel(self, interaction: discord.Interaction):
        """Configure welcome channel"""
        # Allow server owner or admins only
        if not (interaction.user.id == interaction.guild.owner_id or
                interaction.user.guild_permissions.administrator):
            embed = create_error_embed(
                "Missing Permissions",
                "You need `Administrator` permission to configure channels."
            )
            if interaction.response.is_done():
                await interaction.followup.edit_message(interaction.message.id, embed=embed, view=self)
            else:
                await interaction.response.edit_message(embed=embed, view=self)
            return

        modal = WelcomeChannelModal()
        await interaction.response.send_modal(modal)

    async def _config_activity_channel(self, interaction: discord.Interaction):
        """Configure activity channel"""
        # Allow server owner or admins only
        if not (interaction.user.id == interaction.guild.owner_id or
                interaction.user.guild_permissions.administrator):
            embed = create_error_embed(
                "Missing Permissions",
                "You need `Administrator` permission to configure channels."
            )
            if interaction.response.is_done():
                await interaction.followup.edit_message(interaction.message.id, embed=embed, view=self)
            else:
                await interaction.response.edit_message(embed=embed, view=self)
            return

        modal = ActivityChannelModal()
        await interaction.response.send_modal(modal)

    async def _config_milestone_channel(self, interaction: discord.Interaction):
        """Configure milestone channel"""
        # Allow server owner or admins only
        if not (interaction.user.id == interaction.guild.owner_id or
                interaction.user.guild_permissions.administrator):
            embed = create_error_embed(
                "Missing Permissions",
                "You need `Administrator` permission to configure channels."
            )
            if interaction.response.is_done():
                await interaction.followup.edit_message(interaction.message.id, embed=embed, view=self)
            else:
                await interaction.response.edit_message(embed=embed, view=self)
            return

        modal = MilestoneChannelModal()
        await interaction.response.send_modal(modal)

    async def _config_counts(self, interaction: discord.Interaction):
        """Configure message counts"""
        # Allow server owner or admins only
        if not (interaction.user.id == interaction.guild.owner_id or
                interaction.user.guild_permissions.administrator):
            embed = create_error_embed(
                "Missing Permissions",
                "You need `Administrator` permission to configure settings."
            )
            if interaction.response.is_done():
                await interaction.followup.edit_message(interaction.message.id, embed=embed, view=self)
            else:
                await interaction.response.edit_message(embed=embed, view=self)
            return

        modal = MessageCountsModal()
        await interaction.response.send_modal(modal)

    async def _config_roles(self, interaction: discord.Interaction):
        """Configure role-based permissions"""
        # Check permissions using the new permission system
        from utils.permissions import permission_manager
        if not await permission_manager.has_bot_admin_access(interaction.user):
            error_msg = await permission_manager.get_permission_error_message(
                interaction.user, "bot_admin"
            )
            embed = create_error_embed("Access Denied", error_msg)
            if interaction.response.is_done():
                await interaction.followup.edit_message(interaction.message.id, embed=embed, view=self)
            else:
                await interaction.response.edit_message(embed=embed, view=self)
            return

        # Show role configuration modal
        from interactions.role_config import RoleConfigModal
        modal = await RoleConfigModal.create(interaction.guild.id)
        await interaction.response.send_modal(modal)

    async def _config_bonus_entries(self, interaction: discord.Interaction):
        """Configure role-based bonus entries"""
        # Check permissions using the new permission system
        from utils.permissions import permission_manager
        if not await permission_manager.has_bot_admin_access(interaction.user):
            error_msg = await permission_manager.get_permission_error_message(
                interaction.user, "bot_admin"
            )
            embed = create_error_embed("Access Denied", error_msg)
            if interaction.response.is_done():
                await interaction.followup.edit_message(interaction.message.id, embed=embed, view=self)
            else:
                await interaction.response.edit_message(embed=embed, view=self)
            return

        # Get current bonus multipliers
        role_multipliers = await db_manager.get_role_bonus_multipliers(interaction.guild.id)

        # Create bonus entries configuration view
        from interactions.bonus_entries_views import BonusEntriesConfigView
        from config.messages import EmbedTitles, GiveawayMessages

        # Build description with current configuration
        description = (
            "Configure which roles receive bonus entries in giveaways!\n\n"
            f"{GiveawayMessages.BONUS_ENTRY_INFO}\n\n"
        )

        if role_multipliers:
            description += "**Current Bonus Roles:**\n"
            for role_id_str, multiplier in role_multipliers.items():
                role = interaction.guild.get_role(int(role_id_str))
                role_name = role.name if role else f"Unknown Role ({role_id_str})"
                description += f"• **{role_name}**: {multiplier}x entries\n"
            description += "\n"
        else:
            description += "**No bonus roles configured yet.**\n\n"

        description += "Use the buttons below to manage bonus entries:"

        embed = InfoEmbed(
            EmbedTitles.BONUS_ENTRIES_CONFIG,
            description
        ).build()

        view = BonusEntriesConfigView(interaction.user.id, interaction.guild.id)

        if interaction.response.is_done():
            await interaction.followup.edit_message(interaction.message.id, embed=embed, view=view)
        else:
            await interaction.response.edit_message(embed=embed, view=view)

    async def _config_contact_support(self, interaction: discord.Interaction):
        """Configure contact support settings"""
        # Check permissions using the new permission system
        from utils.permissions import permission_manager
        if not await permission_manager.has_bot_admin_access(interaction.user):
            error_msg = await permission_manager.get_permission_error_message(
                interaction.user, "bot_admin"
            )
            embed = create_error_embed("Access Denied", error_msg)
            if interaction.response.is_done():
                await interaction.followup.edit_message(interaction.message.id, embed=embed, view=self)
            else:
                await interaction.response.edit_message(embed=embed, view=self)
            return

        # Get current contact support configuration
        contact_config = await db_manager.get_contact_support_config(interaction.guild.id)

        # Build description with current configuration
        description = (
            "Configure how users can contact server administrators for support!\n\n"
            "**Current Configuration:**\n"
        )

        if contact_config['contact_message']:
            description += f"**Custom Message:** {contact_config['contact_message'][:100]}{'...' if len(contact_config['contact_message']) > 100 else ''}\n"
        else:
            description += "**Custom Message:** *Using default message*\n"

        if contact_config['contact_discord_server']:
            description += f"**Support Server:** {contact_config['contact_discord_server']}\n"
        else:
            description += "**Support Server:** *Not configured*\n"

        if contact_config['contact_website']:
            description += f"**Support Website:** {contact_config['contact_website']}\n"
        else:
            description += "**Support Website:** *Not configured*\n"

        if contact_config['contact_email']:
            description += f"**Support Email:** {contact_config['contact_email']}\n"
        else:
            description += "**Support Email:** *Not configured*\n"

        if contact_config['contact_methods']:
            description += f"\n**Contact Methods:** {len(contact_config['contact_methods'])} configured\n"
            methods_preview = "\n".join([f"• {method}" for method in contact_config['contact_methods'][:3]])
            if len(contact_config['contact_methods']) > 3:
                methods_preview += f"\n• ... and {len(contact_config['contact_methods']) - 3} more"
            description += f"{methods_preview}\n"
        else:
            description += "\n**Contact Methods:** *Using default methods*\n"

        description += "\n*Use the buttons below to configure contact support settings:*"

        embed = InfoEmbed(
            "📞 Contact Support Configuration",
            description
        ).build()

        # Create contact support configuration view
        from interactions.views.contact_support_config import ContactSupportConfigView
        view = ContactSupportConfigView(interaction.user.id, interaction.guild.id)

        if interaction.response.is_done():
            await interaction.followup.edit_message(interaction.message.id, embed=embed, view=view)
        else:
            await interaction.response.edit_message(embed=embed, view=view)

    async def _view_config(self, interaction: discord.Interaction):
        """View current server configuration"""
        # Allow server owner, admins, or users with manage messages
        if not (interaction.user.id == interaction.guild.owner_id or
                interaction.user.guild_permissions.administrator or
                interaction.user.guild_permissions.manage_messages):
            embed = create_error_embed(
                "Missing Permissions",
                "You need `Manage Messages` permission or higher to view configuration."
            )
            # Use followup for ephemeral messages
            if interaction.response.is_done():
                await interaction.followup.edit_message(interaction.message.id, embed=embed, view=self)
            else:
                await interaction.response.edit_message(embed=embed, view=self)
            return

        config = await db_manager.get_guild_config(interaction.guild.id)

        embed = InfoEmbed(
            f"⚙️ Server Configuration - {interaction.guild.name}"
        )

        if config:
            # Welcome channel
            welcome_ch = interaction.guild.get_channel(config['welcome_channel']) if config['welcome_channel'] else None
            embed.add_field(
                name="👋 Welcome Channel",
                value=welcome_ch.mention if welcome_ch else "Not configured",
                inline=False
            )

            # Activity channel
            activity_ch = interaction.guild.get_channel(config['activity_channel']) if config['activity_channel'] else None
            embed.add_field(
                name="💬 Activity Channel",
                value=activity_ch.mention if activity_ch else "Not configured",
                inline=False
            )

            # Milestone channel
            milestone_ch = interaction.guild.get_channel(config['milestone_channel']) if config['milestone_channel'] else None
            embed.add_field(
                name="🎊 Milestone Channel",
                value=milestone_ch.mention if milestone_ch else "Not configured",
                inline=False
            )

            # Settings
            embed.add_field(
                name="📈 Activity Message Count",
                value=str(config['activity_message_count']),
                inline=True
            )
            embed.add_field(
                name="🎯 Milestone Count",
                value=str(config['milestone_count']),
                inline=True
            )

            # Role-based permissions
            try:
                import json
                giveaway_roles = json.loads(config.get('giveaway_participant_roles', '[]'))
                admin_roles = json.loads(config.get('bot_admin_roles', '[]'))
                require_auth = bool(config.get('require_authorization', 1))

                # Giveaway participant roles
                giveaway_role_mentions = []
                for role_id in giveaway_roles:
                    role = interaction.guild.get_role(role_id)
                    if role:
                        giveaway_role_mentions.append(role.mention)

                embed.add_field(
                    name="🎉 Giveaway Participant Roles",
                    value=', '.join(giveaway_role_mentions) if giveaway_role_mentions else "No restrictions",
                    inline=False
                )

                # Bot admin roles
                admin_role_mentions = []
                for role_id in admin_roles:
                    role = interaction.guild.get_role(role_id)
                    if role:
                        admin_role_mentions.append(role.mention)

                embed.add_field(
                    name="🔧 Bot Admin Roles",
                    value=', '.join(admin_role_mentions) if admin_role_mentions else "Server owner only",
                    inline=False
                )

                embed.add_field(
                    name="🔐 Authorization Required",
                    value="Yes" if require_auth else "No",
                    inline=True
                )

            except (json.JSONDecodeError, TypeError):
                embed.add_field(
                    name="🔐 Role Permissions",
                    value="Default settings (authorization required)",
                    inline=False
                )
        else:
            embed.set_description("No configuration found. Use the **Server Setup** button to configure.")

        # Update page state and setup back button
        self.current_page = DashboardPage.VIEW_CONFIG
        self._setup_sub_page()

        # Use followup for ephemeral messages
        if interaction.response.is_done():
            await interaction.followup.edit_message(interaction.message.id, embed=embed.build(), view=self)
        else:
            await interaction.response.edit_message(embed=embed.build(), view=self)

    async def _partner_servers_section(self, interaction: discord.Interaction):
        """Show partner servers management dashboard"""
        # Check permissions using the new permission system
        from utils.permissions import permission_manager
        if not await permission_manager.has_bot_admin_access(interaction.user):
            error_msg = await permission_manager.get_permission_error_message(
                interaction.user, "bot_admin"
            )
            embed = create_error_embed("Access Denied", error_msg)
            if interaction.response.is_done():
                await interaction.followup.edit_message(interaction.message.id, embed=embed, view=self)
            else:
                await interaction.response.edit_message(embed=embed, view=self)
            return

        # Get current partner servers
        partner_ids = await db_manager.get_partner_servers(interaction.guild.id)
        
        description = (
            "**Manual Member Addition**\n\n"
            "Add authorized users to specific Discord servers using stored OAuth tokens.\n\n"
            "**How it works:**\n"
            "• Users authorize via `/authorize` command (OAuth tokens stored)\n"
            "• Admins select users and target server via dashboard\n"
            "• Bot adds members directly using their OAuth access tokens\n"
            "• No invites needed - direct server membership\n\n"
        )
        
        if partner_ids:
            description += f"**Currently configured:** {len(partner_ids)} partner servers\n\n"
            
            # Show first few servers as preview
            server_preview = []
            for server_id in partner_ids[:3]:
                from core.bot import bot
                partner_guild = bot.get_guild(server_id)
                if partner_guild:
                    server_preview.append(f"• **{partner_guild.name}** ({partner_guild.member_count:,} members)")
                else:
                    server_preview.append(f"• **Unknown Server** (ID: {server_id})")
            
            description += "\n".join(server_preview)
            if len(partner_ids) > 3:
                description += f"\n• ... and {len(partner_ids) - 3} more servers"
        else:
            description += "**No partner servers configured yet.**"
        
        description += "\n\n*Use the buttons below to manage partner servers:*"

        embed = InfoEmbed(
            "Partner Servers Management",
            description
        ).build()

        # Setup partner server management buttons
        self.current_page = DashboardPage.PARTNER_SERVERS
        self._setup_partner_servers_buttons()

        if interaction.response.is_done():
            await interaction.followup.edit_message(interaction.message.id, embed=embed, view=self)
        else:
            await interaction.response.edit_message(embed=embed, view=self)

    def _setup_partner_servers_buttons(self):
        """Setup buttons for partner servers management"""
        self.clear_items()

        # Row 0 - Management actions
        self.add_item(discord.ui.Button(
            label="Add Server",
            style=discord.ButtonStyle.success,
            emoji="➕",
            row=0,
            custom_id="add_partner_server"
        ))
        self.add_item(discord.ui.Button(
            label="Remove Server",
            style=discord.ButtonStyle.danger,
            emoji="➖",
            row=0,
            custom_id="remove_partner_server"
        ))
        self.add_item(discord.ui.Button(
            label="View All Servers",
            style=discord.ButtonStyle.secondary,
            emoji="📋",
            row=0,
            custom_id="list_partner_servers"
        ))

        # Row 1 - Back button
        self.add_item(discord.ui.Button(
            label="Back to Configuration",
            style=discord.ButtonStyle.secondary,
            emoji="🔙",
            row=1,
            custom_id="configuration_section"
        ))

        # Set up callbacks
        for item in self.children:
            if hasattr(item, 'custom_id') and hasattr(item, 'callback'):
                item.callback = self._handle_button_click

    async def _add_partner_server(self, interaction: discord.Interaction):
        """Show modal to add a partner server"""
        modal = PartnerServerModal(title="Add Partner Server", action="add")
        await interaction.response.send_modal(modal)

    async def _remove_partner_server(self, interaction: discord.Interaction):
        """Show modal to remove a partner server"""
        modal = PartnerServerModal(title="Remove Partner Server", action="remove")
        await interaction.response.send_modal(modal)

    async def _list_partner_servers(self, interaction: discord.Interaction):
        """Show detailed list of all partner servers"""
        partner_ids = await db_manager.get_partner_servers(interaction.guild.id)
        
        if not partner_ids:
            embed = InfoEmbed(
                "Partner Servers",
                "No partner servers configured.\n\n"
                "Use **Add Server** to add partner servers for auto-joining."
            ).build()
            await interaction.response.edit_message(embed=embed, view=self)
            return

        # Build detailed server list
        from core.bot import bot
        server_list = []
        working_servers = 0
        
        for server_id in partner_ids:
            partner_guild = bot.get_guild(server_id)
            if partner_guild:
                working_servers += 1
                # Check bot permissions
                bot_member = partner_guild.get_member(bot.user.id)
                perms_status = "Can invite" if (bot_member and bot_member.guild_permissions.create_instant_invite) else "Missing perms"
                
                server_list.append(
                    f"**{partner_guild.name}**\n"
                    f"┣ ID: `{server_id}`\n"
                    f"┣ Members: {partner_guild.member_count:,}\n"
                    f"┗ Status: {perms_status}"
                )
            else:
                server_list.append(
                    f"**Unknown Server**\n"
                    f"┣ ID: `{server_id}`\n"
                    f"┗ Status: Bot not in server"
                )

        embed = InfoEmbed(
            f"Partner Servers ({len(partner_ids)} total, {working_servers} working)",
            "\n\n".join(server_list) + "\n\n" +
            "**Legend:**\n"
            "• Can invite - Bot can add members to this server\n"
            "• Missing perms - Bot needs invite permissions\n"
            "• Bot not in server - Bot must join the server first\n\n"
            "**How manual addition works:**\n"
            "• User authorizes via `/authorize` (OAuth tokens stored)\n"
            "• Admin selects users and target server via dashboard\n"
            "• Bot adds members directly using stored OAuth tokens\n"
            "• No invites needed - direct server membership"
        ).build()

        await interaction.response.edit_message(embed=embed, view=self)


# All button methods are now handled by the navigation system above

def setup_slash_commands(bot: commands.Bot):
    """Setup modern slash commands"""
    
    @bot.tree.command(name="ping", description="🏓 Check bot latency and status")
    async def ping_slash(interaction: discord.Interaction):
        """Modern ping command with sleek embed"""
        latency = round(bot.latency * 1000)
        
        # Create status indicator
        if latency < 100:
            status = "🟢 Excellent"
            color = Colors.SUCCESS
        elif latency < 200:
            status = "🟡 Good"
            color = Colors.WARNING
        else:
            status = "🔴 Poor"
            color = Colors.ERROR
        
        embed = discord.Embed(
            title="🏓 Pong!",
            description=f"**Latency:** {latency}ms\n**Status:** {status}",
            color=color
        )
        embed.set_footer(text="Memory-optimized bot with modern UI")
        
        await interaction.response.send_message(embed=embed)
        logger.debug(f"Slash ping used by {interaction.user} - {latency}ms")
    
    @bot.tree.command(name="authorize", description="🔐 Get authorization link for giveaway participation")
    async def authorize_slash(interaction: discord.Interaction):
        """Authorization command with server joining permissions"""
        # Import configuration
        from config.messages import AuthorizationConfig, EmbedTitles, get_authorization_embed_description

        # Create OAuth URL using configuration
        auth_url = AuthorizationConfig.get_oauth_url()

        # Create a rich embed with proper formatting
        embed = discord.Embed(
            title="🔐 Giveaway Authorization",
            description="**Unlock Giveaway Benefits**",
            color=0x5865F2  # Discord blurple color
        )
        
        # Add benefits section
        embed.add_field(
            name="What you'll receive:",
            value="• Participation in all giveaways\n• Joins to partner communities\n• Pings for special events + access to member channels",
            inline=False
        )
        
        # Add steps section
        embed.add_field(
            name="Steps:",
            value="1️⃣ Click the authorization link\n2️⃣ Use `/confirm_auth` command after authorization",
            inline=False
        )
        
        # Add note and authorization link
        embed.add_field(
            name="Note:",
            value=f"This allows us to invite you to partner servers and verify your eligibility.\n\n[🚀 Enable Features]({auth_url})",
            inline=False
        )
        
        embed.add_field(
            name="After setup, use `/confirm_auth` to activate.",
            value="\u200b",  # Zero-width space for empty value
            inline=False
        )

        await interaction.response.send_message(embed=embed, ephemeral=True)
        logger.debug(f"Authorization link sent to {interaction.user}")

    @bot.tree.command(name="confirm_auth", description="✅ Confirm your authorization")
    async def confirm_auth_slash(interaction: discord.Interaction):
        """Confirm user authorization with OAuth verification"""
        # Import configuration
        from config.messages import AuthorizationConfig, EmbedTitles, get_confirmation_embed_description

        # Check if already authorized
        if await db_manager.is_user_authorized(interaction.user.id):
            embed = create_success_embed(
                EmbedTitles.ALREADY_AUTHORIZED,
                "Your authorization is already confirmed! You can now participate in giveaways."
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        # Verify OAuth authorization first
        is_verified = await AuthorizationConfig.verify_oauth_authorization(bot, interaction.user.id)

        if not is_verified:
            embed = create_error_embed(
                EmbedTitles.AUTH_FAILED,
                "❌ **Authorization verification failed!**\n\n"
                "It looks like you haven't completed the OAuth authorization yet.\n\n"
                "**Please:**\n"
                "1️⃣ Use `/authorize` to get the authorization link\n"
                "2️⃣ Complete the Discord authorization process\n"
                "3️⃣ Then run this command again\n\n"
                "**Note:** You must actually authorize the bot through Discord!"
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
            logger.warning(f"OAuth verification failed for {interaction.user}")
            return

        # Get OAuth tokens from server if available
        access_token = None
        refresh_token = None
        expires_in = None
        
        try:
            from utils.oauth_server import get_oauth_server
            oauth_server = get_oauth_server()
            if oauth_server and str(interaction.user.id) in oauth_server.pending_authorizations:
                oauth_data = oauth_server.pending_authorizations[str(interaction.user.id)]
                access_token = oauth_data.get('access_token')
                refresh_token = oauth_data.get('refresh_token')
                expires_in = oauth_data.get('expires_in', 604800)  # Default 7 days
                logger.info(f"Retrieved OAuth tokens for user {interaction.user.id}")
        except Exception as e:
            logger.warning(f"Could not retrieve OAuth tokens for user {interaction.user.id}: {e}")

        # Add user to authorized list with OAuth tokens
        success = await db_manager.add_authorized_user(
            interaction.user.id, 
            str(interaction.user),
            access_token=access_token,
            refresh_token=refresh_token,
            expires_in=expires_in
        )

        if success:
            embed = create_success_embed(
                EmbedTitles.AUTH_CONFIRMED,
                get_confirmation_embed_description()
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
            logger.info(f"User authorized via slash command (verified): {interaction.user}")
        else:
            embed = create_error_embed(
                EmbedTitles.AUTH_FAILED,
                "Something went wrong. Please try again! 🔄"
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)

    @bot.tree.command(name="test_oauth", description="🔍 Test OAuth verification status (for debugging)")
    async def test_oauth_slash(interaction: discord.Interaction):
        """Test OAuth verification status"""
        from config.messages import AuthorizationConfig

        is_verified = await AuthorizationConfig.verify_oauth_authorization(bot, interaction.user.id)

        embed = InfoEmbed(
            "🔍 OAuth Verification Test",
            f"**User:** {interaction.user.mention}\n"
            f"**OAuth Verified:** {'✅ Yes' if is_verified else '❌ No'}\n"
            f"**Already Authorized:** {'✅ Yes' if await db_manager.is_user_authorized(interaction.user.id) else '❌ No'}\n\n"
            f"**What this means:**\n"
            f"• OAuth Verified = Bot can verify you completed Discord authorization\n"
            f"• Already Authorized = You're in the bot's authorized users database"
        ).build()

        await interaction.response.send_message(embed=embed, ephemeral=True)

    @bot.tree.command(name="clear_my_auth", description="🗑️ Clear your own authorization (for testing)")
    async def clear_my_auth_slash(interaction: discord.Interaction):
        """Clear your own authorization for testing"""
        try:
            async with db_manager.get_connection() as conn:
                cursor = await conn.execute(
                    'DELETE FROM authorized_users WHERE user_id = ?',
                    (interaction.user.id,)
                )
                await conn.commit()

                if cursor.rowcount > 0:
                    embed = create_success_embed(
                        "✅ Authorization Cleared",
                        f"Your authorization has been cleared.\n\n"
                        f"You can now test the OAuth verification by trying to authorize again!"
                    )
                else:
                    embed = InfoEmbed(
                        "ℹ️ No Authorization Found",
                        "You weren't in the authorized users list."
                    ).build()

                await interaction.response.send_message(embed=embed, ephemeral=True)

        except Exception as e:
            logger.error(f"Error clearing authorization: {e}")
            embed = create_error_embed(
                "Error",
                "Something went wrong clearing your authorization."
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)

    @bot.tree.command(name="dashboard", description="🎛️ Open the comprehensive bot dashboard")
    async def dashboard_slash(interaction: discord.Interaction):
        """Comprehensive bot dashboard with all features"""
        # Check permissions
        from utils.permissions import permission_manager
        if not await permission_manager.check_dashboard_access(interaction):
            return
        embed = InfoEmbed(
            "🎛️ Bot Dashboard",
            f"Dashboard access granted for {interaction.user.mention}\n\n"
            f"**🎉 Giveaway Features:**\n"
            f"• Create giveaways with all settings\n"
            f"• Quick instant giveaways for engagement\n"
            f"• Interactive entry system with buttons\n"
            f"• Role-based bonus entries system\n\n"
            f"**👥 Community Management:**\n"
            f"• View member statistics\n"
            f"• Community network expansion tools\n"
            f"• Community growth system\n\n"
            f"**⚙️ Server Configuration:**\n"
            f"• Welcome notification setup\n"
            f"• Activity-based giveaways\n"
            f"• Milestone celebrations\n"
            f"• Custom prize emoji configuration\n"
            f"• All customizable from Discord\n\n"
            f"*Click the buttons below to access features*"
        ).build()

        view = ComprehensiveDashboard(interaction.user.id)
        await interaction.response.send_message(embed=embed, view=view, ephemeral=True)

        logger.debug(f"Dashboard opened by {interaction.user}")

    @bot.tree.command(name="auth_status", description="🔍 Check your current authorization status")
    async def auth_status_slash(interaction: discord.Interaction):
        """Check user's authorization status"""
        is_authorized = await db_manager.is_user_authorized(interaction.user.id)

        # Check guild configuration
        require_auth = await db_manager.get_require_authorization(interaction.guild.id)
        participant_roles = await db_manager.get_giveaway_participant_roles(interaction.guild.id)

        # Check if user can participate
        from utils.permissions import PermissionManager
        can_participate = await PermissionManager.can_participate_in_giveaways(interaction.user)

        # Build status message
        status_lines = []
        status_lines.append(f"**Authorization Status:** {'✅ Authorized' if is_authorized else '❌ Not Authorized'}")
        status_lines.append(f"**Can Participate in Giveaways:** {'✅ Yes' if can_participate else '❌ No'}")
        status_lines.append("")

        # Guild configuration
        status_lines.append("**Server Configuration:**")
        status_lines.append(f"• Authorization Required: {'Yes' if require_auth else 'No'}")

        if participant_roles:
            role_mentions = []
            for role_id in participant_roles:
                role = interaction.guild.get_role(role_id)
                if role:
                    role_mentions.append(role.mention)
            status_lines.append(f"• Required Roles: {', '.join(role_mentions) if role_mentions else 'None configured'}")
        else:
            status_lines.append("• Required Roles: No role restrictions")

        # User roles
        user_role_mentions = [role.mention for role in interaction.user.roles if not role.is_default()]
        status_lines.append(f"• Your Roles: {', '.join(user_role_mentions) if user_role_mentions else 'No roles'}")

        embed = InfoEmbed(
            "🔍 Authorization Status",
            "\n".join(status_lines)
        ).build()

        await interaction.response.send_message(embed=embed, ephemeral=True)

    @bot.tree.command(name="clear_auth", description="🗑️ Clear your authorization (admin only)")
    async def clear_auth_slash(interaction: discord.Interaction, user: discord.Member = None):
        """Clear authorization for a user (admin only)"""
        # Check if user has admin permissions
        from utils.permissions import PermissionManager
        if not await PermissionManager.has_bot_admin_access(interaction.user):
            embed = create_error_embed(
                "Access Denied",
                "Only server administrators can clear user authorization."
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        target_user = user or interaction.user

        # Remove from authorized users
        try:
            async with db_manager.get_connection() as conn:
                cursor = await conn.execute(
                    'DELETE FROM authorized_users WHERE user_id = ?',
                    (target_user.id,)
                )
                await conn.commit()

                if cursor.rowcount > 0:
                    embed = create_success_embed(
                        "✅ Authorization Cleared",
                        f"Successfully cleared authorization for {target_user.mention}.\n\n"
                        f"They will need to run `/authorize` and `/confirm_auth` again to participate in giveaways."
                    )
                else:
                    embed = InfoEmbed(
                        "ℹ️ No Authorization Found",
                        f"{target_user.mention} was not in the authorized users list."
                    ).build()

        except Exception as e:
            logger.error(f"Error clearing authorization: {e}")
            embed = create_error_embed(
                "Database Error",
                "Failed to clear authorization. Please try again."
            )

        await interaction.response.send_message(embed=embed, ephemeral=True)

    @bot.tree.command(name="contact_config", description="Configure contact support settings (admin only)")
    async def contact_config_slash(interaction: discord.Interaction):
        """Configure contact support settings"""
        # Check if user has admin permissions
        from utils.permissions import permission_manager
        if not await permission_manager.has_bot_admin_access(interaction.user):
            embed = create_error_embed(
                "Access Denied",
                "Only server administrators can configure contact support settings."
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        # Get current contact support configuration
        contact_config = await db_manager.get_contact_support_config(interaction.guild.id)

        # Build description with current configuration
        description = (
            "Configure how users can contact server administrators for support!\n\n"
            "**Current Configuration:**\n"
        )

        if contact_config['contact_message']:
            description += f"**Custom Message:** {contact_config['contact_message'][:100]}{'...' if len(contact_config['contact_message']) > 100 else ''}\n"
        else:
            description += "**Custom Message:** *Using default message*\n"

        if contact_config['contact_discord_server']:
            description += f"**Support Server:** {contact_config['contact_discord_server']}\n"
        else:
            description += "**Support Server:** *Not configured*\n"

        if contact_config['contact_website']:
            description += f"**Support Website:** {contact_config['contact_website']}\n"
        else:
            description += "**Support Website:** *Not configured*\n"

        if contact_config['contact_email']:
            description += f"**Support Email:** {contact_config['contact_email']}\n"
        else:
            description += "**Support Email:** *Not configured*\n"

        if contact_config['contact_methods']:
            description += f"\n**Contact Methods:** {len(contact_config['contact_methods'])} configured\n"
            methods_preview = "\n".join([f"• {method}" for method in contact_config['contact_methods'][:3]])
            if len(contact_config['contact_methods']) > 3:
                methods_preview += f"\n• ... and {len(contact_config['contact_methods']) - 3} more"
            description += f"{methods_preview}\n"
        else:
            description += "\n**Contact Methods:** *Using default methods*\n"

        description += "\n*Use the buttons below to configure contact support settings:*"

        embed = InfoEmbed(
            "Contact Support Configuration",
            description
        ).build()

        # Create contact support configuration view
        from interactions.views.contact_support_config import ContactSupportConfigView
        view = ContactSupportConfigView(interaction.user.id, interaction.guild.id)

        await interaction.response.send_message(embed=embed, view=view, ephemeral=True)

    @bot.tree.command(name="contact_preview", description="Preview contact support information")
    async def contact_preview_slash(interaction: discord.Interaction):
        """Preview how the contact information will look"""
        from config.messages import ContactMessages

        try:
            # Generate contact info preview
            contact_info = await ContactMessages.get_contact_info(interaction.guild)

            embed = InfoEmbed(
                "Contact Information Preview",
                f"This is how users will see the contact information:\n\n{contact_info}"
            ).build()

            await interaction.response.send_message(embed=embed, ephemeral=True)

        except Exception as e:
            logger.error(f"Error generating contact info preview: {e}")
            embed = create_error_embed(
                "Preview Error",
                "Failed to generate contact information preview. Please try again."
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)

    @bot.tree.command(name="oauth_status", description="🔍 Check OAuth server status (admin only)")
    async def oauth_status_slash(interaction: discord.Interaction):
        """Check OAuth server status"""
        # Check if user is admin
        if not (interaction.user.guild_permissions.administrator or 
                interaction.user.id == interaction.guild.owner_id):
            embed = create_error_embed(
                "Permission Denied",
                "This command requires administrator permissions."
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return
        
        from utils.oauth_server import get_oauth_server_status
        status = await get_oauth_server_status()
        
        # Create status embed
        if status['status'] == 'running':
            embed = InfoEmbed(
                "✅ OAuth Server Status: Running",
                f"**Server Info:**\n"
                f"• Host: `{status['server_info']['host']}`\n"
                f"• Port: `{status['server_info']['port']}`\n"
                f"• Redirect URI: `{status['server_info']['redirect_uri']}`\n"
                f"• Active Authorizations: `{status['server_info']['authorizations_count']}`\n"
                f"• Health Check: {'✅ Passing' if status['is_responding'] else '❌ Failing'}\n\n"
                f"**Suggestions:**\n" + "\n".join([f"• {s}" for s in status['suggestions']])
            ).build()
            embed.color = 0x00ff00  # Green
            
        elif status['status'] == 'not_running':
            embed = InfoEmbed(
                "❌ OAuth Server Status: Not Running",
                f"**Message:** {status['message']}\n\n"
                f"**Suggestions:**\n" + "\n".join([f"• {s}" for s in status['suggestions']])
            ).build()
            embed.color = 0xff0000  # Red
            
        else:  # unhealthy or error
            embed = InfoEmbed(
                f"⚠️ OAuth Server Status: {status['status'].title()}",
                f"**Message:** {status.get('message', 'Server is unhealthy')}\n"
                f"**Error:** {status.get('error', 'N/A')}\n\n"
                f"**Suggestions:**\n" + "\n".join([f"• {s}" for s in status['suggestions']])
            ).build()
            embed.color = 0xffff00  # Yellow
        
        await interaction.response.send_message(embed=embed, ephemeral=True)

    @bot.tree.command(name="restart_oauth", description="🔄 Restart OAuth server (admin only)")
    async def restart_oauth_slash(interaction: discord.Interaction):
        """Restart the OAuth server"""
        # Check if user is admin
        if not (interaction.user.guild_permissions.administrator or 
                interaction.user.id == interaction.guild.owner_id):
            embed = create_error_embed(
                "Permission Denied",
                "This command requires administrator permissions."
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return
        
        # Defer response as restart might take time
        await interaction.response.defer(ephemeral=True)
        
        from utils.oauth_server import restart_oauth_server
        success = await restart_oauth_server()
        
        if success:
            embed = create_success_embed(
                "✅ OAuth Server Restarted",
                "The OAuth server has been successfully restarted. Authorization should now work properly."
            )
        else:
            embed = create_error_embed(
                "❌ Restart Failed",
                "Failed to restart the OAuth server. Check the logs for more details and ensure DISCORD_CLIENT_SECRET is configured."
            )
        
        await interaction.followup.send(embed=embed, ephemeral=True)

    @bot.tree.command(name="force_sync", description="🔄 Force sync slash commands (admin only)")
    async def force_sync_slash(interaction: discord.Interaction):
        """Force sync slash commands"""
        # Check if user is admin
        if not (interaction.user.guild_permissions.administrator or 
                interaction.user.id == interaction.guild.owner_id):
            embed = create_error_embed(
                "Permission Denied",
                "This command requires administrator permissions."
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return
        
        # Defer response as sync might take time
        await interaction.response.defer(ephemeral=True)
        
        try:
            # Force sync by removing the hash file
            import os
            hash_file = 'data/command_hash.txt'
            if os.path.exists(hash_file):
                os.remove(hash_file)
            
            # Sync commands
            synced = await bot.tree.sync()
            
            embed = create_success_embed(
                "✅ Commands Force Synced",
                f"Successfully synced {len(synced)} slash commands to Discord.\n\n"
                f"**Commands now available:**\n" + 
                "\n".join([f"• `/{cmd.name}` - {cmd.description}" for cmd in synced[:5]]) +
                (f"\n• ... and {len(synced) - 5} more" if len(synced) > 5 else "")
            )
        except Exception as e:
            embed = create_error_embed(
                "❌ Sync Failed",
                f"Failed to sync slash commands: {str(e)}\n\n"
                f"**Common causes:**\n"
                f"• Bot missing `applications.commands` scope\n"
                f"• Rate limited by Discord\n"
                f"• Bot not properly invited to server"
            )
        
        await interaction.followup.send(embed=embed, ephemeral=True)

    @bot.tree.command(name="bot_invite", description="🔗 Get proper bot invitation URL with all scopes")
    async def bot_invite_slash(interaction: discord.Interaction):
        """Get the correct bot invitation URL with all required scopes"""
        # Check if user is admin
        if not (interaction.user.guild_permissions.administrator or 
                interaction.user.id == interaction.guild.owner_id):
            embed = create_error_embed(
                "Permission Denied",
                "This command requires administrator permissions."
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return
        
        from config.messages import AuthorizationConfig
        client_id = AuthorizationConfig.CLIENT_ID
        
        # Bot invitation URL with ALL required scopes
        bot_invite_url = (
            f"https://discord.com/api/oauth2/authorize?"
            f"client_id={client_id}&"
            f"permissions=1084479749184&"  # All needed permissions
            f"scope=bot%20applications.commands"  # Bot + slash commands scopes
        )
        
        embed = InfoEmbed(
            "🔗 Bot Invitation URL",
            "**Use this URL to properly invite the bot with all required scopes:**\n\n"
            f"[**Invite Bot with All Permissions**]({bot_invite_url})\n\n"
            "**This invitation includes:**\n"
            "• `bot` - Basic bot functionality\n"
            "• `applications.commands` - Slash commands support\n"
            "• All necessary permissions for giveaways and auto-join\n\n"
            "**After re-inviting, use `/force_sync` to update commands.**"
        ).build()
        
        await interaction.response.send_message(embed=embed, ephemeral=True)

    @bot.tree.command(name="test_oauth_token", description="🔍 Test if a user's OAuth token is still valid (admin only)")
    async def test_oauth_token_slash(interaction: discord.Interaction, user: discord.Member = None):
        """Test OAuth token validity for a user"""
        # Check if user is admin
        if not (interaction.user.guild_permissions.administrator or 
                interaction.user.id == interaction.guild.owner_id):
            embed = create_error_embed(
                "Permission Denied",
                "This command requires administrator permissions."
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return
        
        target_user = user or interaction.user
        
        # Defer response as token testing might take time
        await interaction.response.defer(ephemeral=True)
        
        from config.messages import AuthorizationConfig
        
        # Check database authorization first
        is_in_db = await db_manager.is_user_authorized(target_user.id)
        
        # Test OAuth token validity (this will also update database if invalid)
        token_valid = await AuthorizationConfig.test_oauth_token_validity(target_user.id)
        
        # Get additional token info from database
        tokens = await db_manager.get_user_oauth_tokens(target_user.id)
        has_tokens_stored = bool(tokens.get('access_token'))
        token_expires_at = tokens.get('token_expires_at')
        last_test = tokens.get('last_token_test')
        
        # Build status message
        status_lines = []
        status_lines.append(f"**User:** {target_user.mention}")
        status_lines.append(f"**Database Authorization:** {'✅ Yes' if is_in_db else '❌ No'}")
        status_lines.append(f"**OAuth Tokens Stored:** {'✅ Yes' if has_tokens_stored else '❌ No'}")
        status_lines.append(f"**OAuth Token Valid:** {'✅ Yes' if token_valid else '❌ No/Unknown'}")
        
        if token_expires_at:
            from datetime import datetime
            try:
                expires = datetime.fromisoformat(token_expires_at.replace('Z', '+00:00'))
                now = datetime.now()
                if expires > now:
                    time_left = expires - now
                    status_lines.append(f"**Token Expires:** In {time_left.days} days, {time_left.seconds//3600} hours")
                else:
                    status_lines.append(f"**Token Expires:** ⚠️ Expired")
            except:
                status_lines.append(f"**Token Expires:** Unknown format")
        
        if last_test:
            status_lines.append(f"**Last Token Test:** {last_test}")
        
        status_lines.append("")
        
        if is_in_db and not token_valid:
            status_lines.append("⚠️ **Warning:** User is in database but OAuth token may be invalid/expired")
            status_lines.append("• User might have revoked access in Discord settings")
            status_lines.append("• Token might have expired (7 days)")
            status_lines.append("• OAuth server might not have their token stored")
        elif not is_in_db and token_valid:
            status_lines.append("⚠️ **Warning:** Valid OAuth token but not in authorized database")
            status_lines.append("• User completed OAuth but never ran `/confirm_auth`")
        elif is_in_db and token_valid:
            status_lines.append("✅ **All Good:** User is properly authorized with valid token")
        else:
            status_lines.append("❌ **Issues:** User needs to complete authorization process")
        
        status_lines.append("")
        status_lines.append("**📋 Important Notes:**")
        status_lines.append("• Discord API doesn't provide direct OAuth status checking")
        status_lines.append("• Token validity is tested by attempting to use it")
        status_lines.append("• Users can revoke access anytime in Discord settings")
        status_lines.append("• OAuth tokens expire after 7 days by default")
        
        embed = InfoEmbed(
            "🔍 OAuth Token Test Results",
            "\n".join(status_lines)
        ).build()
        
        await interaction.followup.send(embed=embed, ephemeral=True)

    @bot.tree.command(name="analytics", description="📊 View comprehensive bot analytics and insights (admin only)")
    async def analytics_slash(interaction: discord.Interaction):
        """Show comprehensive analytics dashboard"""
        # Check permissions
        from utils.permissions import permission_manager
        if not await permission_manager.has_bot_admin_access(interaction.user):
            error_msg = await permission_manager.get_permission_error_message(
                interaction.user, "bot_admin"
            )
            embed = create_error_embed("Access Denied", error_msg)
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return
        
        try:
            # Create analytics embed
            from utils.analytics import SmartAnalytics
            analytics_embed = await SmartAnalytics.create_analytics_embed(interaction.guild.id)
            
            # Create action buttons for detailed views
            view = discord.ui.View(timeout=300)
            
            # Detailed metrics button
            details_button = discord.ui.Button(
                label="📈 Detailed Metrics",
                style=discord.ButtonStyle.primary,
                emoji="📈"
            )
            
            async def show_detailed_metrics(button_interaction):
                try:
                    # Get detailed analytics
                    giveaway_analytics = await SmartAnalytics.get_giveaway_analytics(interaction.guild.id)
                    engagement_metrics = await SmartAnalytics.get_user_engagement_metrics(interaction.guild.id)
                    
                    detailed_embed = discord.Embed(
                        title="📈 Detailed Analytics",
                        color=0x2ecc71,
                        timestamp=datetime.now()
                    )
                    
                    # Add detailed fields
                    detailed_embed.add_field(
                        name="🎯 Giveaway Performance",
                        value=f"**Period:** {giveaway_analytics.get('period', 'Last 30 days')}\n"
                              f"**Total Giveaways:** {giveaway_analytics.get('total_giveaways', 0)}\n"
                              f"**Avg Participants:** {giveaway_analytics.get('avg_participants_per_giveaway', 0):.1f}",
                        inline=True
                    )
                    
                    detailed_embed.add_field(
                        name="👥 User Behavior",
                        value=f"**Retention Rate:** {engagement_metrics.get('retention_rate', 0)}%\n"
                              f"**Avg Session:** {engagement_metrics.get('avg_session_length', 'Unknown')}\n"
                              f"**Active Users:** {engagement_metrics.get('total_authorized_users', 0)}",
                        inline=True
                    )
                    
                    await button_interaction.response.send_message(embed=detailed_embed, ephemeral=True)
                    
                except Exception as e:
                    logger.error(f"Error showing detailed metrics: {e}")
                    await button_interaction.response.send_message("❌ Error loading detailed metrics.", ephemeral=True)
            
            details_button.callback = show_detailed_metrics
            view.add_item(details_button)
            
            # Export data button
            export_button = discord.ui.Button(
                label="📊 Export Data",
                style=discord.ButtonStyle.secondary,
                emoji="📊"
            )
            
            async def export_analytics_data(button_interaction):
                try:
                    # Generate exportable analytics summary
                    engagement_metrics = await SmartAnalytics.get_user_engagement_metrics(interaction.guild.id)
                    insights = await SmartAnalytics.generate_insights_and_recommendations(interaction.guild.id)
                    
                    export_text = f"""📊 **Analytics Export - {interaction.guild.name}**
**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

**Key Metrics:**
• Authorized Users: {engagement_metrics.get('total_authorized_users', 0)}
• OAuth Health: {engagement_metrics.get('oauth_health_percentage', 0):.1f}%
• Valid Tokens: {engagement_metrics.get('valid_oauth_tokens', 0)}
• Expired Tokens: {engagement_metrics.get('expired_oauth_tokens', 0)}

**Insights:**
{chr(10).join(f"• {insight}" for insight in insights[:5])}

**Report ID:** {interaction.id}
"""
                    
                    await button_interaction.response.send_message(
                        f"```\n{export_text}\n```",
                        ephemeral=True
                    )
                    
                except Exception as e:
                    logger.error(f"Error exporting analytics: {e}")
                    await button_interaction.response.send_message("❌ Error exporting data.", ephemeral=True)
            
            export_button.callback = export_analytics_data
            view.add_item(export_button)
            
            await interaction.response.send_message(embed=analytics_embed, view=view, ephemeral=True)
            logger.info(f"Analytics dashboard viewed by {interaction.user}")
            
        except Exception as e:
            logger.error(f"Error in analytics command: {e}")
            embed = create_error_embed(
                "Analytics Error",
                "Unable to generate analytics dashboard. Please try again."
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)

    logger.info("Slash commands loaded (with OAuth management and diagnostics)")
