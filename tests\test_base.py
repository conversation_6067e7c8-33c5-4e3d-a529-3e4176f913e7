"""
Base testing utilities for Discord bot verification.
Provides common setup, mocking, and assertion helpers for all test modules.
"""

import asyncio
import sys
import os
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any, Optional
import discord
from discord.ext import commands

# Add the parent directory to the path to import bot modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class MockGuild:
    """Mock Discord Guild for testing"""
    def __init__(self, guild_id: int = 12345, name: str = "Test Guild", member_count: int = 100):
        self.id = guild_id
        self.name = name
        self.member_count = member_count
        self.me = MockMember(user_id=98765, name="TestBot")
        self.channels = []
        self.roles = []
        self.members = []

class MockMember:
    """Mock Discord Member for testing"""
    def __init__(self, user_id: int = 54321, name: str = "TestUser", guild_id: int = 12345):
        self.id = user_id
        self.name = name
        self.display_name = name
        self.guild = MockGuild(guild_id)
        self.bot = False
        self.roles = []

class MockChannel:
    """Mock Discord Channel for testing"""
    def __init__(self, channel_id: int = 67890, name: str = "test-channel"):
        self.id = channel_id
        self.name = name
        self.send = AsyncMock()
        self.guild = MockGuild()

class MockMessage:
    """Mock Discord Message for testing"""
    def __init__(self, content: str = "test message", author: MockMember = None, channel: MockChannel = None):
        self.id = 11111
        self.content = content
        self.author = author or MockMember()
        self.channel = channel or MockChannel()
        self.guild = self.channel.guild
        self.reactions = []
        self.add_reaction = AsyncMock()
        self.remove_reaction = AsyncMock()

class MockReaction:
    """Mock Discord Reaction for testing"""
    def __init__(self, emoji: str = "✅", count: int = 1, message: MockMessage = None):
        self.emoji = emoji
        self.count = count
        self.message = message or MockMessage()
        self.users = AsyncMock(return_value=[])

class TestConfig:
    """Test configuration and constants"""
    TEST_GUILD_ID = 12345
    TEST_USER_ID = 54321
    TEST_CHANNEL_ID = 67890
    TEST_BOT_ID = 98765
    
    # Database test values
    TEST_DB_PATH = "test_bot_data.db"  # Use file-based SQLite for tests (in-memory has connection issues)
    
    # OAuth test values
    TEST_CLIENT_ID = "123456789"
    TEST_CLIENT_SECRET = "test_secret"
    TEST_REDIRECT_URI = "http://localhost:8080/callback"

class AsyncTestCase:
    """Base class for async test cases"""

    def __init__(self):
        self.loop = None
        self.setup_complete = False

    async def async_setup(self):
        """Override this method for async setup"""
        pass

    async def async_teardown(self):
        """Override this method for async cleanup"""
        pass

    async def run_async_test(self, test_method):
        """Helper to run async test methods - now properly async"""
        if not self.setup_complete:
            await self.async_setup()
            self.setup_complete = True

        try:
            # Call the test method and await its result
            return await test_method()
        finally:
            await self.async_teardown()

def create_mock_bot():
    """Create a mock bot instance for testing"""
    bot = MagicMock()
    bot.user = MockMember(user_id=TestConfig.TEST_BOT_ID, name="TestBot")
    bot.guilds = [MockGuild()]
    bot.get_guild = MagicMock(return_value=MockGuild())
    bot.get_user = MagicMock(return_value=MockMember())
    bot.get_channel = MagicMock(return_value=MockChannel())
    return bot

def assert_embed_valid(embed: discord.Embed, expected_title: str = None, expected_color: int = None):
    """Assert that an embed is properly formatted"""
    assert isinstance(embed, discord.Embed), "Expected discord.Embed object"
    
    if expected_title:
        assert embed.title == expected_title, f"Expected title '{expected_title}', got '{embed.title}'"
    
    if expected_color:
        assert embed.color.value == expected_color, f"Expected color {expected_color}, got {embed.color.value}"
    
    # Ensure embed has some content
    assert embed.title or embed.description or embed.fields, "Embed must have title, description, or fields"

def assert_database_operation_success(result: bool, operation: str):
    """Assert that a database operation completed successfully"""
    assert result is True, f"Database {operation} operation failed"

def print_test_header(test_name: str):
    """Print a formatted test header"""
    print(f"\n{'='*60}")
    print(f"RUNNING TEST: {test_name}")
    print(f"{'='*60}")

def print_test_result(test_name: str, passed: bool, details: str = ""):
    """Print formatted test results"""
    status = "✅ PASSED" if passed else "❌ FAILED"
    print(f"{status}: {test_name}")
    if details:
        print(f"Details: {details}")
    print("-" * 40)

# Context managers for testing
class MockDatabaseContext:
    """Context manager for database testing"""
    def __init__(self):
        self.db_manager = None

    async def __aenter__(self):
        # Import here to avoid circular imports
        from utils.database import DatabaseManager
        import os

        # Clean up any existing test database
        if os.path.exists(TestConfig.TEST_DB_PATH):
            os.remove(TestConfig.TEST_DB_PATH)

        self.db_manager = DatabaseManager(TestConfig.TEST_DB_PATH)
        await self.db_manager.initialize_database()
        return self.db_manager

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        # Clean up test database file
        import os
        if os.path.exists(TestConfig.TEST_DB_PATH):
            try:
                os.remove(TestConfig.TEST_DB_PATH)
            except Exception:
                pass  # Ignore cleanup errors

# Test data generators
def generate_test_guild_config() -> Dict[str, Any]:
    """Generate test guild configuration data"""
    return {
        'guild_id': TestConfig.TEST_GUILD_ID,
        'last_member_count': 100,
        'milestone_count': 500,
        'require_authorization': 1,
        'authorization_role_id': None,
        'welcome_channel_id': None,
        'log_channel_id': None,
        'premium_role_id': None,
        'giveaway_channel_id': None,
        'announcement_channel_id': None
    }

def generate_test_member_data() -> Dict[str, Any]:
    """Generate test member data"""
    return {
        'user_id': TestConfig.TEST_USER_ID,
        'guild_id': TestConfig.TEST_GUILD_ID,
        'username': 'TestUser',
        'discriminator': '1234',
        'avatar_url': 'https://example.com/avatar.png',
        'is_premium': False,
        'join_date': '2024-01-01 00:00:00'
    }

if __name__ == "__main__":
    print("Discord Bot Test Base - Utilities loaded successfully")
    print(f"Test configuration: Guild ID {TestConfig.TEST_GUILD_ID}")
