#!/usr/bin/env python3
"""
Debug script to test database initialization
"""

import asyncio
import sys
import os
sys.path.insert(0, '.')

async def test_database_creation():
    """Test database table creation step by step"""
    print("🔍 Testing Database Creation Step by Step")
    print("=" * 50)

    try:
        from utils.database import DatabaseManager
        import aiosqlite

        # Test with file-based database like the tests now use
        print("Step 1: Creating DatabaseManager...")
        import os
        test_db = "test_debug.db"
        if os.path.exists(test_db):
            os.remove(test_db)
        db = DatabaseManager(test_db)
        print("✅ DatabaseManager created")

        # Test direct table creation without using the initialize_database method
        print("\nStep 2: Testing direct table creation...")
        async with db.get_connection() as conn:
            print("✅ Got database connection")

            # Try creating tables directly
            print("Creating authorized_users table...")
            await conn.execute('''
                CREATE TABLE IF NOT EXISTS authorized_users (
                    user_id INTEGER PRIMARY KEY,
                    username TEXT NOT NULL,
                    authorized_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    access_token TEXT DEFAULT NULL,
                    refresh_token TEXT DEFAULT NULL,
                    token_expires_at TIMESTAMP DEFAULT NULL,
                    last_token_test TIMESTAMP DEFAULT NULL
                )
            ''')

            print("Creating active_giveaways table...")
            await conn.execute('''
                CREATE TABLE IF NOT EXISTS active_giveaways (
                    message_id INTEGER PRIMARY KEY,
                    guild_id INTEGER NOT NULL,
                    channel_id INTEGER NOT NULL,
                    title TEXT NOT NULL,
                    description TEXT,
                    end_time TIMESTAMP NOT NULL,
                    winner_count INTEGER DEFAULT 1,
                    created_by INTEGER NOT NULL,
                    participants TEXT DEFAULT '[]',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            print("Creating guild_config table...")
            await conn.execute('''
                CREATE TABLE IF NOT EXISTS guild_config (
                    guild_id INTEGER PRIMARY KEY,
                    welcome_channel INTEGER,
                    activity_channel INTEGER,
                    activity_message_count INTEGER DEFAULT 50,
                    milestone_channel INTEGER,
                    milestone_count INTEGER DEFAULT 500,
                    last_member_count INTEGER DEFAULT 0,
                    nitro_emoji TEXT DEFAULT '💎',
                    robux_emoji TEXT DEFAULT '🔶',
                    garden_emoji TEXT DEFAULT '🌳',
                    custom_emoji TEXT DEFAULT '🎁',
                    giveaway_participant_roles TEXT DEFAULT '[]',
                    bot_admin_roles TEXT DEFAULT '[]',
                    require_authorization INTEGER DEFAULT 1,
                    role_bonus_multipliers TEXT DEFAULT '{}',
                    partner_servers TEXT DEFAULT '[]',
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            print("Committing changes...")
            await conn.commit()
            print("✅ Tables created and committed")

            # Check what tables were created
            cursor = await conn.execute("""
                SELECT name FROM sqlite_master
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
            """)
            tables = [row[0] for row in await cursor.fetchall()]
            print(f"📋 Tables found after direct creation: {tables}")

        print("\nStep 3: Testing initialize_database method...")
        # Reset the initialized flag to test the method
        db._initialized = False
        await db.initialize_database()
        print("✅ Database initialization completed")

        # Check what tables exist now
        async with db.get_connection() as conn:
            cursor = await conn.execute("""
                SELECT name FROM sqlite_master
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
            """)
            tables = [row[0] for row in await cursor.fetchall()]
            print(f"📋 Tables found after initialize_database: {tables}")

            # Check each expected table
            expected_tables = ['authorized_users', 'guild_config', 'active_giveaways']
            for table in expected_tables:
                cursor = await conn.execute(f"""
                    SELECT name FROM sqlite_master
                    WHERE type='table' AND name='{table}'
                """)
                result = await cursor.fetchone()
                status = "✅ EXISTS" if result else "❌ MISSING"
                print(f"  {table}: {status}")

                if result:
                    # Check table structure
                    cursor = await conn.execute(f"PRAGMA table_info({table})")
                    columns = await cursor.fetchall()
                    column_names = [col[1] for col in columns]
                    print(f"    Columns: {column_names}")

        print("\n🎉 Database test completed successfully!")

        # Cleanup
        if os.path.exists(test_db):
            os.remove(test_db)
        return True

    except Exception as e:
        print(f"❌ Database test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_database_creation())
    sys.exit(0 if success else 1)
