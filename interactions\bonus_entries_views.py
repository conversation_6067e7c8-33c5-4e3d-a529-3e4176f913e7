"""
Bonus entries configuration views and interactive components
"""
import discord
from interactions.views.base import BaseView
from embeds.builders import create_success_embed, create_error_embed, InfoEmbed
from utils.database import db_manager
from core.logging import logger
from config.messages import (
    ButtonLabels, EmbedTitles, SystemConfig
)

class BonusEntriesConfigView(BaseView):
    """Main view for configuring role bonus entries"""
    
    def __init__(self, user_id: int, guild_id: int):
        super().__init__(timeout=SystemConfig.CONFIRMATION_TIMEOUT, user_id=user_id)
        self.guild_id = guild_id
        
    @discord.ui.button(
        label="Add Bonus Role",
        style=discord.ButtonStyle.primary,
        emoji="➕"
    )
    async def add_bonus_role(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Add a new role bonus multiplier"""
        modal = AddBonusRoleModal(self.guild_id)
        await interaction.response.send_modal(modal)

    @discord.ui.button(
        label="Edit Existing",
        style=discord.ButtonStyle.secondary,
        emoji="📝"
    )
    async def edit_existing(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Edit existing role bonus multipliers"""
        # Get current multipliers
        role_multipliers = await db_manager.get_role_bonus_multipliers(self.guild_id)
        
        if not role_multipliers:
            embed = create_error_embed(
                "No Bonus Roles",
                "No bonus roles are currently configured. Use **Add Bonus Role** to create one."
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return
            
        # Create selection view
        view = EditBonusRoleSelectView(self.user_id, self.guild_id, role_multipliers)
        
        # Build embed with current roles
        description = "**Current Bonus Roles:**\n\n"
        guild = interaction.guild
        
        for role_id_str, multiplier in role_multipliers.items():
            role = guild.get_role(int(role_id_str))
            role_name = role.name if role else f"Unknown Role ({role_id_str})"
            description += f"• **{role_name}**: {multiplier}x entries\n"
        
        description += "\nSelect a role to edit or remove:"
        
        embed = InfoEmbed(
            "Edit Bonus Roles",
            description
        ).build()
        
        await interaction.response.send_message(embed=embed, view=view, ephemeral=True)

    @discord.ui.button(
        label="Clear All",
        style=discord.ButtonStyle.danger,
        emoji="🗑️"
    )
    async def clear_all(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Clear all bonus role multipliers"""
        view = ConfirmClearAllView(self.user_id, self.guild_id)

        embed = create_error_embed(
            "⚠️ Clear All Bonus Roles",
            "Are you sure you want to remove ALL role bonus multipliers?\n\n"
            "This action cannot be undone."
        )

        await interaction.response.send_message(embed=embed, view=view, ephemeral=True)

    @discord.ui.button(
        label="Back to Dashboard",
        style=discord.ButtonStyle.secondary,
        emoji="🔙",
        row=1
    )
    async def back_to_dashboard(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Return to the main dashboard"""
        # Import here to avoid circular imports
        from commands.slash_commands import ComprehensiveDashboard, DashboardPage

        # Create new dashboard view
        view = ComprehensiveDashboard(interaction.user.id)
        view.current_page = DashboardPage.MAIN
        view._setup_main_page()

        # Create main dashboard embed
        from embeds.builders import InfoEmbed
        embed = InfoEmbed(
            "🎛️ Bot Dashboard",
            "Welcome to the comprehensive bot dashboard! Choose an option below to get started.\n\n"
            "**🎉 Giveaway Features:**\n"
            "• Create custom giveaways with multiple prize types\n"
            "• Quick giveaways for instant engagement\n"
            "• Role-based bonus entries system\n\n"
            "**⚙️ Server Management:**\n"
            "• Configure bot settings for your server\n"
            "• Add members to partner communities\n"
            "• Customize prize emojis\n\n"
            "**📊 Information:**\n"
            "• View current server configuration\n"
            "• Check server and bot statistics"
        ).build()

        await interaction.response.edit_message(embed=embed, view=view)

class AddBonusRoleModal(discord.ui.Modal):
    """Modal for adding a new bonus role"""
    
    def __init__(self, guild_id: int):
        super().__init__(title="Add Bonus Role")
        self.guild_id = guild_id
        
    role_id = discord.ui.TextInput(
        label="Role ID",
        placeholder="Enter the Discord role ID (right-click role → Copy ID)",
        required=True,
        max_length=20
    )
    
    multiplier = discord.ui.TextInput(
        label="Entry Multiplier",
        placeholder="Enter multiplier (e.g., 2 for 2x entries, 3 for 3x entries)",
        required=True,
        max_length=10
    )
    
    async def on_submit(self, interaction: discord.Interaction):
        """Handle modal submission"""
        try:
            # Validate role ID
            try:
                role_id = int(self.role_id.value.strip())
            except ValueError:
                embed = create_error_embed(
                    "Invalid Role ID",
                    "Please enter a valid Discord role ID (numbers only)."
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
                return
            
            # Validate multiplier
            try:
                multiplier_value = float(self.multiplier.value.strip())
                if multiplier_value <= 0:
                    raise ValueError("Multiplier must be positive")
                if multiplier_value > 100:
                    raise ValueError("Multiplier too large")
            except ValueError:
                embed = create_error_embed(
                    "Invalid Multiplier",
                    "Please enter a valid positive number (e.g., 2, 3, 1.5).\nMaximum value is 100."
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
                return
            
            # Check if role exists in guild
            guild = interaction.guild
            role = guild.get_role(role_id)
            if not role:
                embed = create_error_embed(
                    "Role Not Found",
                    f"No role with ID `{role_id}` found in this server.\n\n"
                    f"**How to get role ID:**\n"
                    f"1. Enable Developer Mode in Discord settings\n"
                    f"2. Right-click the role in server settings\n"
                    f"3. Select 'Copy ID'"
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
                return
            
            # Add the bonus multiplier
            success = await db_manager.add_role_bonus_multiplier(
                self.guild_id, role_id, multiplier_value
            )
            
            if success:
                embed = create_success_embed(
                    EmbedTitles.BONUS_ENTRY_ADDED,
                    f"Successfully added bonus multiplier!\n\n"
                    f"**Role:** {role.mention}\n"
                    f"**Multiplier:** {multiplier_value}x entries\n\n"
                    f"Members with this role will now receive **{int(multiplier_value)} entries** in giveaways."
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
                logger.info(f"Added bonus multiplier: {role.name} ({role_id}) = {multiplier_value}x in guild {self.guild_id}")
            else:
                embed = create_error_embed(
                    "Database Error",
                    "Failed to save the bonus multiplier. Please try again."
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
                
        except Exception as e:
            logger.error(f"Error in AddBonusRoleModal: {e}")
            embed = create_error_embed(
                "Unexpected Error",
                "Something went wrong. Please try again."
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)

class EditBonusRoleSelectView(BaseView):
    """View for selecting which bonus role to edit"""
    
    def __init__(self, user_id: int, guild_id: int, role_multipliers: dict):
        super().__init__(timeout=SystemConfig.CONFIRMATION_TIMEOUT, user_id=user_id)
        self.guild_id = guild_id
        self.role_multipliers = role_multipliers
        
        # Add select menu for roles
        self.add_item(BonusRoleSelect(guild_id, role_multipliers))

        # Add back button
        self.add_item(discord.ui.Button(
            label="🔙 Back to Bonus Config",
            style=discord.ButtonStyle.secondary,
            emoji="🔙",
            custom_id="back_to_bonus_config",
            row=1
        ))

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        """Handle button interactions"""
        if interaction.data.get("custom_id") == "back_to_bonus_config":
            # Go back to main bonus config view
            view = BonusEntriesConfigView(self.user_id, self.guild_id)

            # Get current bonus multipliers for the embed
            role_multipliers = await db_manager.get_role_bonus_multipliers(self.guild_id)

            # Build description with current configuration
            from config.messages import EmbedTitles, GiveawayMessages
            description = (
                "Configure which roles receive bonus entries in giveaways!\n\n"
                f"{GiveawayMessages.BONUS_ENTRY_INFO}\n\n"
            )

            if role_multipliers:
                description += "**Current Bonus Roles:**\n"
                for role_id_str, multiplier in role_multipliers.items():
                    role = interaction.guild.get_role(int(role_id_str))
                    role_name = role.name if role else f"Unknown Role ({role_id_str})"
                    description += f"• **{role_name}**: {multiplier}x entries\n"
                description += "\n"
            else:
                description += "**No bonus roles configured yet.**\n\n"

            description += "Use the buttons below to manage bonus entries:"

            from embeds.builders import InfoEmbed
            embed = InfoEmbed(
                EmbedTitles.BONUS_ENTRIES_CONFIG,
                description
            ).build()

            await interaction.response.edit_message(embed=embed, view=view)
            return False

        return await super().interaction_check(interaction)

class BonusRoleSelect(discord.ui.Select):
    """Select menu for choosing a bonus role to edit"""
    
    def __init__(self, guild_id: int, role_multipliers: dict):
        self.guild_id = guild_id
        self.role_multipliers = role_multipliers
        
        # Create options from current roles
        options = []
        for role_id_str, multiplier in role_multipliers.items():
            # We'll get the role name in the callback since we don't have guild access here
            options.append(discord.SelectOption(
                label=f"Role ID: {role_id_str}",
                description=f"Current multiplier: {multiplier}x entries",
                value=role_id_str
            ))
        
        super().__init__(
            placeholder="Select a role to edit or remove...",
            options=options,
            min_values=1,
            max_values=1
        )
    
    async def callback(self, interaction: discord.Interaction):
        """Handle role selection"""
        role_id_str = self.values[0]
        role_id = int(role_id_str)
        current_multiplier = self.role_multipliers[role_id_str]
        
        # Get role info
        guild = interaction.guild
        role = guild.get_role(role_id)
        role_name = role.name if role else f"Unknown Role ({role_id_str})"
        
        # Create edit view
        view = EditSingleBonusRoleView(interaction.user.id, self.guild_id, role_id, current_multiplier, role_name)
        
        embed = InfoEmbed(
            f"Edit Bonus Role: {role_name}",
            f"**Current Settings:**\n"
            f"• **Role:** {role.mention if role else role_name}\n"
            f"• **Multiplier:** {current_multiplier}x entries\n\n"
            f"What would you like to do?"
        ).build()
        
        await interaction.response.send_message(embed=embed, view=view, ephemeral=True)

class EditSingleBonusRoleView(BaseView):
    """View for editing a single bonus role"""
    
    def __init__(self, user_id: int, guild_id: int, role_id: int, current_multiplier: float, role_name: str):
        super().__init__(timeout=SystemConfig.CONFIRMATION_TIMEOUT, user_id=user_id)
        self.guild_id = guild_id
        self.role_id = role_id
        self.current_multiplier = current_multiplier
        self.role_name = role_name
        
    @discord.ui.button(
        label="✏️ Edit Multiplier", 
        style=discord.ButtonStyle.primary,
        emoji="✏️"
    )
    async def edit_multiplier(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Edit the multiplier value"""
        modal = EditMultiplierModal(self.guild_id, self.role_id, self.current_multiplier, self.role_name)
        await interaction.response.send_modal(modal)
        
    @discord.ui.button(
        label="🗑️ Remove Role", 
        style=discord.ButtonStyle.danger,
        emoji="🗑️"
    )
    async def remove_role(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Remove this bonus role"""
        success = await db_manager.remove_role_bonus_multiplier(self.guild_id, self.role_id)
        
        if success:
            embed = create_success_embed(
                EmbedTitles.BONUS_ENTRY_REMOVED,
                f"Successfully removed bonus multiplier for **{self.role_name}**.\n\n"
                f"This role will no longer provide bonus entries in giveaways."
            )
            await interaction.response.edit_message(embed=embed, view=None)
            logger.info(f"Removed bonus multiplier for role {self.role_name} ({self.role_id}) in guild {self.guild_id}")
        else:
            embed = create_error_embed(
                "Database Error",
                "Failed to remove the bonus multiplier. Please try again."
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)

    @discord.ui.button(
        label="🔙 Back to Role List",
        style=discord.ButtonStyle.secondary,
        emoji="🔙",
        row=1
    )
    async def back_to_role_list(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Go back to the role selection list"""
        # Get current multipliers
        role_multipliers = await db_manager.get_role_bonus_multipliers(self.guild_id)

        if not role_multipliers:
            from embeds.builders import create_error_embed
            embed = create_error_embed(
                "No Bonus Roles",
                "No bonus roles are currently configured."
            )
            await interaction.response.edit_message(embed=embed, view=None)
            return

        # Create selection view
        view = EditBonusRoleSelectView(self.user_id, self.guild_id, role_multipliers)

        # Build embed with current roles
        description = "**Current Bonus Roles:**\n\n"
        guild = interaction.guild

        for role_id_str, multiplier in role_multipliers.items():
            role = guild.get_role(int(role_id_str))
            role_name = role.name if role else f"Unknown Role ({role_id_str})"
            description += f"• **{role_name}**: {multiplier}x entries\n"

        description += "\nSelect a role to edit or remove:"

        from embeds.builders import InfoEmbed
        embed = InfoEmbed(
            "Edit Bonus Roles",
            description
        ).build()

        await interaction.response.edit_message(embed=embed, view=view)

class EditMultiplierModal(discord.ui.Modal):
    """Modal for editing a multiplier value"""
    
    def __init__(self, guild_id: int, role_id: int, current_multiplier: float, role_name: str):
        super().__init__(title=f"Edit Multiplier: {role_name}")
        self.guild_id = guild_id
        self.role_id = role_id
        self.current_multiplier = current_multiplier
        self.role_name = role_name
        
        # Pre-fill with current value
        self.multiplier.default = str(current_multiplier)
        
    multiplier = discord.ui.TextInput(
        label="New Entry Multiplier",
        placeholder="Enter new multiplier (e.g., 2 for 2x entries, 3 for 3x entries)",
        required=True,
        max_length=10
    )
    
    async def on_submit(self, interaction: discord.Interaction):
        """Handle modal submission"""
        try:
            # Validate multiplier
            try:
                multiplier_value = float(self.multiplier.value.strip())
                if multiplier_value <= 0:
                    raise ValueError("Multiplier must be positive")
                if multiplier_value > 100:
                    raise ValueError("Multiplier too large")
            except ValueError:
                embed = create_error_embed(
                    "Invalid Multiplier",
                    "Please enter a valid positive number (e.g., 2, 3, 1.5).\nMaximum value is 100."
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
                return
            
            # Update the bonus multiplier
            success = await db_manager.add_role_bonus_multiplier(
                self.guild_id, self.role_id, multiplier_value
            )
            
            if success:
                embed = create_success_embed(
                    EmbedTitles.BONUS_ENTRIES_UPDATED,
                    f"Successfully updated bonus multiplier!\n\n"
                    f"**Role:** {self.role_name}\n"
                    f"**Old Multiplier:** {self.current_multiplier}x entries\n"
                    f"**New Multiplier:** {multiplier_value}x entries\n\n"
                    f"Members with this role will now receive **{int(multiplier_value)} entries** in giveaways."
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
                logger.info(f"Updated bonus multiplier: {self.role_name} ({self.role_id}) = {multiplier_value}x in guild {self.guild_id}")
            else:
                embed = create_error_embed(
                    "Database Error",
                    "Failed to update the bonus multiplier. Please try again."
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
                
        except Exception as e:
            logger.error(f"Error in EditMultiplierModal: {e}")
            embed = create_error_embed(
                "Unexpected Error",
                "Something went wrong. Please try again."
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)

class ConfirmClearAllView(BaseView):
    """View for confirming clearing all bonus roles"""
    
    def __init__(self, user_id: int, guild_id: int):
        super().__init__(timeout=SystemConfig.CONFIRMATION_TIMEOUT, user_id=user_id)
        self.guild_id = guild_id
        
    @discord.ui.button(
        label="✅ Yes, Clear All", 
        style=discord.ButtonStyle.danger,
        emoji="✅"
    )
    async def confirm_clear(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Confirm clearing all bonus roles"""
        success = await db_manager.update_role_bonus_multipliers(self.guild_id, {})
        
        if success:
            embed = create_success_embed(
                "✅ All Bonus Roles Cleared",
                "Successfully removed all role bonus multipliers.\n\n"
                "All members will now receive 1 entry in giveaways by default."
            )
            await interaction.response.edit_message(embed=embed, view=None)
            logger.info(f"Cleared all bonus multipliers in guild {self.guild_id}")
        else:
            embed = create_error_embed(
                "Database Error",
                "Failed to clear bonus multipliers. Please try again."
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
        
    @discord.ui.button(
        label="❌ Cancel", 
        style=discord.ButtonStyle.secondary,
        emoji="❌"
    )
    async def cancel_clear(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Cancel clearing bonus roles"""
        embed = InfoEmbed(
            "Operation Cancelled",
            "Bonus role configuration remains unchanged."
        ).build()
        
        await interaction.response.edit_message(embed=embed, view=None)
