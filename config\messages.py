"""Centralized configuration for all bot messages and text content
This eliminates hardcoded values and makes the system modular and configurable
"""

import os
from typing import Optional

class AuthorizationConfig:
    """Configuration for authorization-related messages and settings"""

    # Bot's Discord Application Client ID (from Discord Developer Portal)
    CLIENT_ID = "1398041068703977613"

    # Member benefits (used consistently across all authorization messages)
    MEMBER_BENEFITS = [
        "Participation in all giveaways",
        "Eligibility for community server access",
        "Notifications for special events and member-only channels",
        "Verified member status for exclusive content"
    ]

    # Authorization requirements
    REQUIREMENTS = [
        "Must have required server roles",
        "Must complete bot authorization"
    ]

    # OAuth scopes and permissions
    OAUTH_SCOPES = "identify guilds.join"
    OAUTH_PERMISSIONS = "0"
    
    # OAuth2 Configuration
    REDIRECT_URI = os.getenv('REDIRECT_URI', 'http://localhost:8080/callback')  # Read from environment or default to localhost
    CLIENT_SECRET = os.getenv('DISCORD_CLIENT_SECRET')  # Should be set from environment variable
    
    @classmethod
    def get_benefits_text(cls) -> str:
        """Get formatted benefits text"""
        return "\n".join([f"• {benefit}" for benefit in cls.MEMBER_BENEFITS])
    
    @classmethod
    def get_requirements_text(cls) -> str:
        """Get formatted requirements text"""
        return "\n".join([f"• {req}" for req in cls.REQUIREMENTS])
    
    @classmethod
    def get_oauth_url(cls, client_id: str = None, redirect_uri: str = None) -> str:
        """Generate OAuth URL with proper Authorization Code Grant flow"""
        import urllib.parse
        
        # Use configured client ID if none provided
        if client_id is None:
            client_id = cls.CLIENT_ID
            
        # Use configured redirect URI if none provided
        if redirect_uri is None:
            redirect_uri = cls.REDIRECT_URI

        # Properly encode the scope parameter to fix hyperlink issues
        encoded_scopes = urllib.parse.quote(cls.OAUTH_SCOPES.replace(' ', '%20'))
        encoded_redirect_uri = urllib.parse.quote(redirect_uri, safe=':/?#[]@!$&\'()*+,;=')

        # Always use Authorization Code Grant flow (response_type=code)
        return (
            f'https://discord.com/api/oauth2/authorize?'
            f'client_id={client_id}&'
            f'redirect_uri={encoded_redirect_uri}&'
            f'response_type=code&'
            f'scope={encoded_scopes}'
        )

    @classmethod
    async def verify_oauth_authorization(cls, bot, user_id: int) -> bool:
        """
        Verify that a user has actually completed the OAuth authorization.
        Returns True if verified, False otherwise.
        
        Enhanced verification based on working Discord OAuth2 examples.
        """
        try:
            from utils.oauth_server import get_oauth_server
            from utils.database import db_manager
            from core.logging import logger
            
            logger.debug(f"Starting OAuth verification for user {user_id}")
            
            # Primary verification: Check database for stored OAuth tokens
            # This is the most reliable method as it indicates completed OAuth flow
            try:
                tokens = await db_manager.get_user_oauth_tokens(user_id)
                if tokens.get('access_token'):
                    # We have stored tokens - verify they're still valid
                    is_valid = await cls.test_oauth_token_validity(user_id)
                    if is_valid:
                        logger.info(f"✅ OAuth verification successful for user {user_id} - valid tokens")
                        return True
                    else:
                        logger.warning(f"⚠️  User {user_id} has tokens but they're invalid/expired")
                        # Continue to check other methods
                
            except Exception as e:
                logger.error(f"Error checking stored tokens for user {user_id}: {e}")
            
            # Secondary verification: Check OAuth server active authorizations
            oauth_server = get_oauth_server()
            if oauth_server:
                is_authorized = oauth_server.is_user_authorized(str(user_id))
                if is_authorized:
                    logger.info(f"✅ OAuth verification successful for user {user_id} - active in OAuth server")
                    return True
                else:
                    logger.debug(f"User {user_id} not found in OAuth server active authorizations")
            else:
                logger.debug("OAuth server not running - unable to check active authorizations")
            
            # Fallback verification: Check if user is in authorized users database
            # This is the least reliable but helps with legacy authorizations
            try:
                is_authorized_in_db = await db_manager.is_user_authorized(user_id)
                if is_authorized_in_db:
                    logger.info(f"✅ OAuth verification successful for user {user_id} - found in authorized users database")
                    return True
                else:
                    logger.debug(f"User {user_id} not found in authorized users database")
                    
            except Exception as e:
                logger.error(f"Error in database verification for user {user_id}: {e}")
            
            # Final check: Test with Discord API if we have any stored token
            # This catches edge cases where tokens exist but aren't properly validated
            try:
                user_data = await cls._get_user_from_discord_api(user_id)
                if user_data:
                    logger.info(f"✅ OAuth verification successful for user {user_id} - confirmed via Discord API")
                    return True
            except Exception as e:
                logger.debug(f"Discord API verification failed for user {user_id}: {e}")
            
            logger.warning(f"❌ OAuth verification failed for user {user_id} - not authorized")
            return False
                
        except Exception as e:
            from core.logging import logger
            logger.error(f"Error in OAuth verification for user {user_id}: {e}")
            return False
    
    @classmethod
    async def test_oauth_token_validity(cls, user_id: int) -> bool:
        """
        Test if a user's OAuth token is still valid by attempting to use it.
        This is the only way to verify token validity with Discord API.
        
        Returns True if token works, False if invalid/expired.
        """
        try:
            from utils.database import db_manager
            from utils.oauth_server import get_oauth_server
            from core.logging import logger
            
            # First check if we have tokens stored in database
            tokens = await db_manager.get_user_oauth_tokens(user_id)
            access_token = tokens.get('access_token')
            
            if not access_token:
                # Fallback: try OAuth server
                oauth_server = get_oauth_server()
                if oauth_server:
                    access_token = oauth_server.get_user_token(str(user_id))
                
                if not access_token:
                    logger.warning(f"No OAuth token found for user {user_id}")
                    await db_manager.mark_token_test(user_id, False)
                    return False
            
            # Test the token by making a simple API call
            import aiohttp
            headers = {'Authorization': f'Bearer {access_token}'}
            
            async with aiohttp.ClientSession() as session:
                async with session.get('https://discord.com/api/users/@me', headers=headers) as resp:
                    if resp.status == 200:
                        user_info = await resp.json()
                        if str(user_info.get('id')) == str(user_id):
                            logger.info(f"OAuth token validation successful for user {user_id}")
                            await db_manager.mark_token_test(user_id, True)
                            return True
                        else:
                            logger.warning(f"OAuth token validation failed - wrong user for {user_id}")
                            await db_manager.mark_token_test(user_id, False)
                            return False
                    elif resp.status == 401:
                        logger.warning(f"OAuth token invalid/expired for user {user_id}")
                        await db_manager.mark_token_test(user_id, False)
                        return False
                    else:
                        logger.warning(f"OAuth token test failed with status {resp.status} for user {user_id}")
                        return False
                
        except Exception as e:
            from core.logging import logger
            logger.error(f"Error testing OAuth token for user {user_id}: {e}")
            await db_manager.mark_token_test(user_id, False)
            return False
    
    @classmethod
    async def _get_user_from_discord_api(cls, user_id: int) -> Optional[dict]:
        """
        Helper method to get user data from Discord API using stored tokens.
        Based on working Discord OAuth2 examples from the web.
        """
        try:
            from utils.database import db_manager
            from core.logging import logger
            import aiohttp
            
            # Get stored tokens
            tokens = await db_manager.get_user_oauth_tokens(user_id)
            access_token = tokens.get('access_token')
            
            if not access_token:
                logger.debug(f"No access token found for user {user_id}")
                return None
            
            # Make API request following working examples
            headers = {
                'Authorization': f'Bearer {access_token}',
                'User-Agent': 'DiscordBot (https://github.com/your-bot, 1.0)',
                'Accept': 'application/json'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get('https://discord.com/api/users/@me', headers=headers) as resp:
                    if resp.status == 200:
                        user_data = await resp.json()
                        # Verify the user ID matches
                        if str(user_data.get('id')) == str(user_id):
                            logger.debug(f"Successfully verified user {user_id} via Discord API")
                            return user_data
                        else:
                            logger.warning(f"Discord API returned different user ID for {user_id}")
                            return None
                    elif resp.status == 401:
                        logger.warning(f"Access token invalid for user {user_id}")
                        # Mark token as invalid in database
                        await db_manager.mark_token_test(user_id, False)
                        return None
                    else:
                        logger.warning(f"Discord API error {resp.status} for user {user_id}")
                        return None
                        
        except Exception as e:
            from core.logging import logger
            logger.error(f"Error getting user data from Discord API for {user_id}: {e}")
            return None

class GiveawayMessages:
    """Configuration for giveaway-related messages"""

    # Entry instructions
    ENTRY_INSTRUCTIONS = [
        "Click the **Enter Giveaway** button below",
        "Must have required roles and authorization (use `/authorize`)",
        "Await the giveaway conclusion"
    ]

    # Quick giveaway instructions
    QUICK_ENTRY_INSTRUCTIONS = [
        "First authorized member to respond will be selected as the winner",
        "Must have required roles and completed `/authorize` to be eligible"
    ]

    # Bonus entry messages
    BONUS_ENTRY_INFO = "🎯 **Bonus Entries:** Some roles receive extra entries!"
    BONUS_ENTRY_DETAILS = "Your role gives you **{entries}x entries** in this giveaway!"
    NO_BONUS_MESSAGE = "You have **1 entry** in this giveaway."
    
    # Warning messages
    WARNING_TITLE = "⚠️ Giveaway Entry Denied"
    QUICK_WARNING_TITLE = "⚠️ Quick Giveaway Entry Denied"
    
    WARNING_FOOTER = (
        "**Need Help?**\n"
        "• Use the **Quick Authorization** button below\n"
        "• Contact server administrators for role assignment\n"
        "• Use the **Contact Admins** button for assistance"
    )
    
    LOGGING_WARNING = "**⚠️ Warning:** Unauthorized participation attempts are logged."
    
    @classmethod
    def get_entry_instructions(cls) -> str:
        """Get formatted entry instructions"""
        return "\n".join([f"{i+1}️⃣ {instruction}" for i, instruction in enumerate(cls.ENTRY_INSTRUCTIONS)])
    
    @classmethod
    def get_quick_instructions(cls) -> str:
        """Get formatted quick giveaway instructions"""
        return "\n".join([f"• {instruction}" for instruction in cls.QUICK_ENTRY_INSTRUCTIONS])
    
    @classmethod
    def get_warning_message(cls, is_quick: bool = False) -> str:
        """Get complete warning message"""
        title = cls.QUICK_WARNING_TITLE if is_quick else cls.WARNING_TITLE
        giveaway_type = "quick giveaway" if is_quick else "giveaway"
        
        return (
            f"You cannot participate in this {giveaway_type}.\n\n"
            f"**Requirements:**\n"
            f"{AuthorizationConfig.get_requirements_text()}\n\n"
            f"{cls.LOGGING_WARNING}\n\n"
            f"**📋 Authorization Benefits:**\n"
            f"{AuthorizationConfig.get_benefits_text()}\n\n"
            f"{cls.WARNING_FOOTER}"
        )

class ButtonLabels:
    """Configuration for button labels and emojis"""
    
    # Giveaway buttons
    ENTER_GIVEAWAY = "🎉 Enter Giveaway"
    ENTER_QUICK_GIVEAWAY = "⚡ Enter Quick Giveaway"
    
    # Authorization buttons
    QUICK_AUTHORIZATION = "🚀 Quick Authorization"
    START_AUTHORIZATION = "🚀 Start Authorization"
    CONFIRM_AUTHORIZATION = "✅ Confirm Authorization"
    CANCEL_AUTHORIZATION = "❌ Cancel"
    
    # Contact buttons
    CONTACT_ADMINS = "📞 Contact Admins"
    
    # Dashboard buttons
    ROLE_PERMISSIONS = "🔐 Role Permissions"
    GIVEAWAY_CONFIG = "🎉 Giveaway Settings"
    MEMBER_STATS = "👥 Member Statistics"
    BONUS_ENTRIES = "🎯 Bonus Entries"

class EmbedTitles:
    """Configuration for embed titles"""
    
    # Authorization embeds
    AUTHORIZATION_SETUP = "🔐 Giveaway Authorization"
    QUICK_AUTH_SETUP = "🔐 Quick Authorization Setup"
    AUTH_CONFIRMED = "✅ Authorization Confirmed"
    AUTH_FAILED = "Authorization Failed"
    AUTH_CANCELLED = "Authorization Cancelled"
    ALREADY_AUTHORIZED = "✅ Already Authorized"
    
    # Contact embeds
    CONTACT_ADMINS = "📞 Contact Server Administrators"
    
    # Giveaway embeds
    GIVEAWAY_CREATED = "🎉 Giveaway Created Successfully"
    QUICK_GIVEAWAY = "⚡ Quick Giveaway Active"

    # Bonus entry embeds
    BONUS_ENTRIES_CONFIG = "🎯 Bonus Entries Configuration"
    BONUS_ENTRIES_UPDATED = "✅ Bonus Entries Updated Successfully"
    BONUS_ENTRY_ADDED = "✅ Bonus Entry Added Successfully"
    BONUS_ENTRY_REMOVED = "✅ Bonus Entry Removed Successfully"

class ContactMessages:
    """Configuration for contact and help messages"""

    # Default contact methods - can be customized per server
    DEFAULT_CONTACT_METHODS = [
        "Send a direct message to the server owner",
        "Ask in the server's general chat",
        "Look for moderator or admin channels",
        "Check server rules for contact information"
    ]

    # Configurable contact message
    CUSTOM_CONTACT_MESSAGE = "For additional support, please contact the server administrators."

    # Support information
    SUPPORT_INFO = {
        "discord_server": None,  # Can be set to a Discord server invite
        "website": None,         # Can be set to a support website
        "email": None           # Can be set to a support email
    }
    
    @classmethod
    async def get_contact_info(cls, guild) -> str:
        """Generate contact information for a guild"""
        from utils.database import db_manager

        # Get guild-specific contact configuration
        contact_config = await db_manager.get_contact_support_config(guild.id)

        # Try to get owner by ID first, then fallback to guild.owner
        owner = None
        if guild.owner_id:
            try:
                owner = await guild.fetch_member(guild.owner_id)
            except:
                owner = guild.owner
        else:
            owner = guild.owner

        admin_roles = [role for role in guild.roles if role.permissions.administrator and not role.is_bot_managed()]
        admin_mentions = [role.mention for role in admin_roles[:3]]  # Limit to 3 roles

        contact_info = f"**Server Owner:** {owner.mention if owner else f'<@{guild.owner_id}>' if guild.owner_id else 'Not available'}\n"

        if admin_mentions:
            contact_info += f"**Admin Roles:** {', '.join(admin_mentions)}\n"

        # Use configured contact methods or defaults
        contact_methods = contact_config['contact_methods'] if contact_config['contact_methods'] else cls.DEFAULT_CONTACT_METHODS

        contact_info += f"\n**How to get help:**\n"
        contact_info += "\n".join([f"• {method}" for method in contact_methods])

        # Add custom contact message if configured
        custom_message = contact_config['contact_message'] or cls.CUSTOM_CONTACT_MESSAGE
        if custom_message:
            contact_info += f"\n\n{custom_message}"

        # Add additional support info if configured
        support_links = []
        if contact_config['contact_discord_server']:
            support_links.append(f"[Support Server]({contact_config['contact_discord_server']})")
        elif cls.SUPPORT_INFO["discord_server"]:
            support_links.append(f"[Support Server]({cls.SUPPORT_INFO['discord_server']})")

        if contact_config['contact_website']:
            support_links.append(f"[Support Website]({contact_config['contact_website']})")
        elif cls.SUPPORT_INFO["website"]:
            support_links.append(f"[Support Website]({cls.SUPPORT_INFO['website']})")

        if contact_config['contact_email']:
            support_links.append(f"Email: {contact_config['contact_email']}")
        elif cls.SUPPORT_INFO["email"]:
            support_links.append(f"Email: {cls.SUPPORT_INFO['email']}")

        if support_links:
            contact_info += f"\n\n**Additional Support:**\n"
            contact_info += "\n".join([f"• {link}" for link in support_links])

        return contact_info

class SystemConfig:
    """Configuration for system-wide settings"""

    # Timeouts (in seconds)
    AUTHORIZATION_TIMEOUT = 300  # 5 minutes
    CONFIRMATION_TIMEOUT = 600   # 10 minutes
    GIVEAWAY_VIEW_TIMEOUT = None  # No timeout for giveaway views

    # Limits
    MAX_ADMIN_ROLES_DISPLAY = 3
    MAX_INVITE_USERS_PER_COMMAND = 50
    MAX_CLEANUP_MESSAGES = 100

    # Default values
    DEFAULT_ACTIVITY_MESSAGE_COUNT = 50
    DEFAULT_MILESTONE_COUNT = 500
    DEFAULT_LAST_MEMBER_COUNT = 0

    # Time units for giveaway duration parsing
    TIME_UNITS = {'s': 1, 'm': 60, 'h': 3600, 'd': 86400}

    # Default quick giveaway prizes
    QUICK_GIVEAWAY_PRIZES = [
        '💎 Special Gems',
        '🎁 Mystery Prize',
        '⭐ Temporary Special Role',
        '🏆 Public Recognition',
        '🎨 Custom Avatar',
        '💰 Server Currency'
    ]

# Convenience functions for common message patterns
def get_authorization_embed_description(include_steps: bool = True) -> str:
    """Get standard authorization embed description"""
    description = (
        f"**Authorization Required for Giveaway Participation**\n\n"
        f"To participate in giveaways, you must authorize this bot. This authorization provides:\n\n"
        f"**Access to the following:**\n"
        f"{AuthorizationConfig.get_benefits_text()}\n\n"
    )

    if include_steps:
        description += (
            f"**Authorization Process:**\n"
            f"1. Click the authorization link below\n"
            f"2. Grant Discord permissions when prompted\n"
            f"3. You will become eligible for community server access\n"
            f"4. Return here and use `/confirm_auth` to complete setup\n\n"
        )

    description += f"**Important:** This authorization enables community access and giveaway participation. You may be added to partner servers when selected."

    return description

def get_confirmation_embed_description() -> str:
    """Get standard confirmation embed description"""
    return (
        f"**Authorization Complete - Community Access Granted**\n\n"
        f"Your authorization has been successfully processed. You now have:\n\n"
        f"• Full participation rights in all giveaways\n"
        f"• Membership in partner Discord servers\n"
        f"• Access to special events and member-only channels\n"
        f"• Community networking and exclusive content access\n\n"
        f"You can now participate in giveaways and access all community features."
    )
