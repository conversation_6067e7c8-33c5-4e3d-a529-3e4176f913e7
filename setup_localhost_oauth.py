#!/usr/bin/env python3
"""
OAuth2 Localhost Setup Script
Helps configure the Discord bot for localhost OAuth2 development
"""

import os
import sys
from pathlib import Path

def check_file_exists(filename):
    """Check if a file exists"""
    return Path(filename).exists()

def create_env_file():
    """Create .env file from template if it doesn't exist"""
    if not check_file_exists('.env'):
        if check_file_exists('.env.example'):
            import shutil
            shutil.copy('.env.example', '.env')
            print("✅ Created .env file from .env.example")
            return True
        else:
            print("❌ .env.example not found")
            return False
    else:
        print("✅ .env file already exists")
        return True

def check_oauth_config():
    """Check OAuth2 configuration"""
    print("\n🔍 Checking OAuth2 Configuration...")
    
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        print("❌ python-dotenv not installed. Run: pip install python-dotenv")
        return False
    
    # Check required environment variables
    required_vars = {
        'DISCORD_TOKEN': 'Your Discord bot token',
        'DISCORD_CLIENT_SECRET': 'Your Discord OAuth2 client secret',
        'REDIRECT_URI': 'OAuth2 redirect URI (should be http://localhost:8080/callback)',
    }
    
    missing_vars = []
    placeholder_vars = []
    
    for var, description in required_vars.items():
        value = os.getenv(var)
        if not value:
            missing_vars.append(f"  • {var}: {description}")
        elif 'your_' in value.lower() or 'here' in value.lower():
            placeholder_vars.append(f"  • {var}: {description}")
    
    if missing_vars:
        print("❌ Missing environment variables:")
        print("\n".join(missing_vars))
        return False
    
    if placeholder_vars:
        print("⚠️  Variables with placeholder values:")
        print("\n".join(placeholder_vars))
        print("\nPlease update your .env file with real values!")
        return False
    
    print("✅ All OAuth2 environment variables are configured!")
    return True

def test_oauth_server():
    """Test if OAuth server can start"""
    print("\n🧪 Testing OAuth2 Server...")
    
    try:
        import asyncio
        import aiohttp
        from utils.oauth_server import OAuth2Server
        from config.messages import AuthorizationConfig
        
        client_id = AuthorizationConfig.CLIENT_ID
        client_secret = os.getenv('DISCORD_CLIENT_SECRET')
        redirect_uri = os.getenv('REDIRECT_URI', 'http://localhost:8080/callback')
        
        # Create test server instance
        test_server = OAuth2Server(client_id, client_secret, redirect_uri)
        
        print(f"✅ OAuth2 server configuration looks valid")
        print(f"📍 Redirect URI: {redirect_uri}")
        print(f"🆔 Client ID: {client_id[:8]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ OAuth2 server test failed: {e}")
        return False

def show_discord_setup_instructions():
    """Show Discord Developer Portal setup instructions"""
    print("\n📋 Discord Developer Portal Setup Instructions:")
    print("="*60)
    print("1. Go to https://discord.com/developers/applications")
    print("2. Select your bot application")
    print("3. Go to OAuth2 > General")
    print("4. Copy the 'Client Secret' and paste it in your .env file")
    print("5. Go to OAuth2 > URL Generator")
    print("6. Add this redirect URI: http://localhost:8080/callback")
    print("7. Make sure these scopes are selected: identify, guilds.join")
    print("8. Save your changes")
    print("="*60)

def main():
    """Main setup function"""
    print("🔐 Discord OAuth2 Localhost Setup")
    print("="*40)
    
    # Step 1: Create .env file
    print("\n1. Setting up environment file...")
    if not create_env_file():
        print("❌ Failed to create .env file")
        return False
    
    # Step 2: Check configuration
    print("\n2. Checking configuration...")
    config_ok = check_oauth_config()
    
    # Step 3: Test OAuth server
    if config_ok:
        print("\n3. Testing OAuth2 server...")
        test_oauth_server()
    
    # Step 4: Show instructions
    if not config_ok:
        show_discord_setup_instructions()
        print("\n💡 After updating your .env file, run this script again to test!")
    else:
        print("\n🎉 OAuth2 localhost setup is complete!")
        print("🚀 You can now start your bot with: python main.py")
        print("📱 Users can authorize at: http://localhost:8080/callback")
    
    return config_ok

if __name__ == "__main__":
    main() 