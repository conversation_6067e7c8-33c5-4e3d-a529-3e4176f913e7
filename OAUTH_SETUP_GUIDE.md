# Discord OAuth Setup Guide

## Current Issue
You're getting an "unsupported_response_type" error because the OAuth URL is not properly configured for your bot's use case.

## Quick Fix (Already Applied)
I've updated the OAuth URL generation in `config/messages.py` to use the proper bot invite format instead of the OAuth code flow when no redirect URI is provided.

## How Discord OAuth Works for Bots

### Option 1: Simple Bot Invite (Recommended for your use case)
This is what your bot is now using. The URL format is:
```
https://discord.com/api/oauth2/authorize?client_id=YOUR_BOT_ID&scope=guilds.join&permissions=0
```

This allows users to:
- Authorize your bot to invite them to servers
- Grant the `guilds.join` permission
- No redirect URI needed
- No response_type needed

### Option 2: Full OAuth Flow (More Complex)
If you want to implement a full OAuth flow with a web server:

1. **Set up redirect URI in Discord Developer Portal:**
   - Go to https://discord.com/developers/applications
   - Select your bot application
   - Go to "OAuth2" section
   - Add redirect URIs (e.g., `http://localhost:8080/callback`)

2. **Update your bot to handle OAuth callbacks:**
   - Set up a web server to handle the redirect
   - Exchange the authorization code for an access token
   - Use the access token to make API calls

## Current Bot Configuration

Your bot is currently configured to use the simple bot invite method, which should work fine for:
- Inviting users to partner servers
- Verifying user authorization
- Giveaway participation

## Testing the Fix

1. Restart your bot
2. Try using the authorization button in a giveaway
3. The OAuth URL should now work without the "unsupported_response_type" error

## If You Still Get Errors

If you continue to get OAuth errors, check:
1. Your bot's client ID is correct
2. Your bot has the necessary permissions in the Discord Developer Portal
3. The `guilds.join` scope is enabled for your bot

## Discord Developer Portal Settings

Make sure your bot application has:
- **Bot** section: Bot token generated
- **OAuth2** section: 
  - Scopes: `bot`, `guilds.join`
  - Bot Permissions: Whatever permissions your bot needs
- **General Information**: Application ID matches your bot's client ID
