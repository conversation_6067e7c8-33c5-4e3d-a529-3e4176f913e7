"""
Permission utilities for role-based access control
"""
import discord
from typing import Union, List
from utils.database import db_manager
import logging

logger = logging.getLogger(__name__)

class PermissionManager:
    """Manages role-based permissions for the bot"""
    
    @staticmethod
    async def is_server_owner(user: discord.Member) -> bool:
        """Check if user is the server owner"""
        return user.guild.owner_id == user.id
    
    @staticmethod
    async def has_bot_admin_access(user: discord.Member) -> bool:
        """Check if user has bot admin access (owner or configured admin roles)"""
        # Server owner always has access
        if await PermissionManager.is_server_owner(user):
            return True
        
        # Check configured admin roles
        admin_role_ids = await db_manager.get_bot_admin_roles(user.guild.id)
        user_role_ids = [role.id for role in user.roles]
        
        return any(role_id in user_role_ids for role_id in admin_role_ids)
    
    @staticmethod
    async def can_participate_in_giveaways(user: discord.Member) -> bool:
        """Check if user can participate in giveaways"""
        guild_id = user.guild.id
        
        # Get configuration
        require_auth = await db_manager.get_require_authorization(guild_id)
        participant_role_ids = await db_manager.get_giveaway_participant_roles(guild_id)
        
        # If no roles configured, fall back to authorization check
        if not participant_role_ids:
            if require_auth:
                return await db_manager.is_user_authorized(user.id)
            else:
                return True  # No restrictions
        
        # Check if user has any of the required roles
        user_role_ids = [role.id for role in user.roles]
        has_required_role = any(role_id in user_role_ids for role_id in participant_role_ids)
        
        # If user has required role, check if authorization is also needed
        if has_required_role:
            if require_auth:
                return await db_manager.is_user_authorized(user.id)
            else:
                return True
        
        return False

    @staticmethod
    async def calculate_bonus_entries(user: discord.Member) -> int:
        """Calculate bonus entries for a user based on their roles"""
        try:
            guild_id = user.guild.id
            role_multipliers = await db_manager.get_role_bonus_multipliers(guild_id)

            if not role_multipliers:
                return 1  # Default 1 entry

            # Get user's role IDs
            user_role_ids = [str(role.id) for role in user.roles]

            # Find the highest multiplier from user's roles
            max_multiplier = 1.0
            for role_id in user_role_ids:
                if role_id in role_multipliers:
                    multiplier = float(role_multipliers[role_id])
                    max_multiplier = max(max_multiplier, multiplier)

            # Convert to integer entries (round down to avoid fractional entries)
            return int(max_multiplier)

        except Exception as e:
            logger.error(f"Error calculating bonus entries for {user}: {e}")
            return 1  # Default to 1 entry on error

    @staticmethod
    async def get_user_bonus_info(user: discord.Member) -> dict:
        """Get detailed bonus information for a user"""
        try:
            guild_id = user.guild.id
            role_multipliers = await db_manager.get_role_bonus_multipliers(guild_id)

            if not role_multipliers:
                return {
                    'total_entries': 1,
                    'bonus_roles': [],
                    'highest_multiplier': 1.0,
                    'highest_role': None
                }

            user_role_ids = [role.id for role in user.roles]
            bonus_roles = []
            max_multiplier = 1.0
            highest_role = None

            for role in user.roles:
                role_id_str = str(role.id)
                if role_id_str in role_multipliers:
                    multiplier = float(role_multipliers[role_id_str])
                    bonus_roles.append({
                        'role': role,
                        'multiplier': multiplier
                    })
                    if multiplier > max_multiplier:
                        max_multiplier = multiplier
                        highest_role = role

            return {
                'total_entries': int(max_multiplier),
                'bonus_roles': bonus_roles,
                'highest_multiplier': max_multiplier,
                'highest_role': highest_role
            }

        except Exception as e:
            logger.error(f"Error getting bonus info for {user}: {e}")
            return {
                'total_entries': 1,
                'bonus_roles': [],
                'highest_multiplier': 1.0,
                'highest_role': None
            }

    @staticmethod
    async def get_permission_error_message(user: discord.Member, permission_type: str) -> str:
        """Get appropriate error message for permission denial"""
        if permission_type == "giveaway_participation":
            guild_id = user.guild.id
            require_auth = await db_manager.get_require_authorization(guild_id)
            participant_role_ids = await db_manager.get_giveaway_participant_roles(guild_id)
            
            if not participant_role_ids:
                # No roles configured, just authorization
                if require_auth:
                    return (
                        "🔐 **Authorization Required**\n\n"
                        "You need to authorize the bot to participate in giveaways.\n\n"
                        "**How to authorize:**\n"
                        "• Use `/authorize` to get your authorization link\n"
                        "• Complete the authorization process\n"
                        "• Then you can participate in all giveaways!"
                    )
                else:
                    return "❌ You don't have permission to participate in giveaways."
            else:
                # Roles configured
                role_mentions = []
                for role_id in participant_role_ids:
                    role = user.guild.get_role(role_id)
                    if role:
                        role_mentions.append(role.mention)
                
                message = (
                    "🔐 **Role Required**\n\n"
                    f"You need one of these roles to participate in giveaways:\n"
                    f"• {', '.join(role_mentions) if role_mentions else 'Configured roles'}\n\n"
                )
                
                if require_auth:
                    message += (
                        "**Additionally, you must:**\n"
                        "• Use `/authorize` to complete authorization\n\n"
                    )
                
                message += "Contact a server administrator for role assignment."
                return message
        
        elif permission_type == "bot_admin":
            return (
                "🔐 **Admin Access Required**\n\n"
                "Only the server owner or users with configured admin roles can access the bot dashboard.\n\n"
                "Contact the server owner for access."
            )
        
        return "❌ You don't have permission to perform this action."
    
    @staticmethod
    async def check_dashboard_access(interaction: discord.Interaction) -> bool:
        """Check if user can access the dashboard and respond with error if not"""
        if not isinstance(interaction.user, discord.Member):
            await interaction.response.send_message(
                "❌ This command can only be used in a server.",
                ephemeral=True
            )
            return False
        
        if not await PermissionManager.has_bot_admin_access(interaction.user):
            error_msg = await PermissionManager.get_permission_error_message(
                interaction.user, "bot_admin"
            )
            
            from embeds.builders import create_error_embed
            embed = create_error_embed("Access Denied", error_msg)
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return False
        
        return True

# Create global instance
permission_manager = PermissionManager()
