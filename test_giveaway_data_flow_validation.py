#!/usr/bin/env python3
"""
Comprehensive validation of giveaway creation data flow.
This script validates that all required fields are correctly passed between modules.
"""

import sys
import os
from datetime import datetime, timedelta

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def analyze_giveaway_view_requirements():
    """Analyze what fields are required by ModernGiveawayView and QuickGiveawayView"""
    print("🔍 Analyzing Giveaway View Requirements")
    print("=" * 60)
    
    # Based on code analysis, these are the fields accessed by the views:
    required_fields = {
        'ModernGiveawayView': {
            'message_id': 'int - Used for database operations and participant tracking',
            'title': 'str - Used for display in analytics and sharing',
            'end_time': 'datetime - Not directly accessed but stored',
            'winner_count': 'int - Not directly accessed but stored',
            'guild_id': 'int - Not directly accessed but stored',
            'channel_id': 'int - Not directly accessed but stored',
            'created_by': 'int - Not directly accessed but stored'
        },
        'QuickGiveawayView': {
            'message_id': 'int - Used for database operations when ending giveaway',
            'title': 'str - Used for winner notification (prize name extraction)',
            'end_time': 'datetime - Used to check if giveaway has expired',
            'winner_count': 'int - Not directly accessed but stored',
            'guild_id': 'int - Not directly accessed but stored',
            'channel_id': 'int - Not directly accessed but stored',
            'created_by': 'int - Not directly accessed but stored'
        }
    }
    
    print("\n📋 Required Fields Analysis:")
    for view_name, fields in required_fields.items():
        print(f"\n🎯 {view_name}:")
        for field, description in fields.items():
            print(f"   • {field}: {description}")
    
    return required_fields

def validate_data_structure(giveaway_data: dict, view_type: str, source: str) -> tuple:
    """Validate giveaway_data structure against requirements"""
    required_fields = {
        'ModernGiveawayView': ['message_id', 'title', 'end_time', 'winner_count', 'guild_id', 'channel_id', 'created_by'],
        'QuickGiveawayView': ['message_id', 'title', 'end_time', 'winner_count', 'guild_id', 'channel_id', 'created_by']
    }
    
    missing_fields = []
    extra_fields = []
    type_errors = []
    
    # Check for missing required fields
    for field in required_fields[view_type]:
        if field not in giveaway_data:
            missing_fields.append(field)
    
    # Check for extra fields (not necessarily errors, but worth noting)
    for field in giveaway_data:
        if field not in required_fields[view_type]:
            extra_fields.append(field)
    
    # Check data types
    expected_types = {
        'message_id': int,
        'title': str,
        'end_time': (datetime, type(None)),  # Can be None initially
        'winner_count': int,
        'guild_id': int,
        'channel_id': int,
        'created_by': int,
        'description': str,  # Optional field
        'is_quick': bool,  # Optional field for quick giveaways
        'requirements': (str, type(None))  # Optional field for quick giveaways
    }
    
    for field, value in giveaway_data.items():
        if field in expected_types:
            expected_type = expected_types[field]
            if isinstance(expected_type, tuple):
                if not isinstance(value, expected_type):
                    type_errors.append(f"{field}: expected {expected_type}, got {type(value)}")
            else:
                if not isinstance(value, expected_type):
                    type_errors.append(f"{field}: expected {expected_type}, got {type(value)}")
    
    return missing_fields, extra_fields, type_errors

def test_giveaway_data_structures():
    """Test all giveaway data structures from different creation paths"""
    print("\n🧪 Testing Giveaway Data Structures")
    print("=" * 60)
    
    # Sample data structures from each creation path
    test_cases = {
        'commands/giveaway.py (regular)': {
            'data': {
                'message_id': 0,
                'guild_id': 123456789,
                'channel_id': 987654321,
                'title': 'Test Prize',
                'description': 'Test Description',
                'end_time': datetime.now() + timedelta(hours=1),
                'winner_count': 1,
                'created_by': 555666777
            },
            'view_type': 'ModernGiveawayView'
        },
        'commands/giveaway.py (quick)': {
            'data': {
                'message_id': 0,
                'guild_id': 123456789,
                'channel_id': 987654321,
                'title': 'Quick Prize',
                'description': 'Quick Description',
                'end_time': datetime.now() + timedelta(minutes=5),
                'winner_count': 1,
                'created_by': 555666777
            },
            'view_type': 'ModernGiveawayView'  # Note: quick_giveaway command uses ModernGiveawayView
        },
        'interactions/modals.py (regular)': {
            'data': {
                'message_id': 0,
                'guild_id': 123456789,
                'channel_id': 987654321,
                'title': 'Modal Prize',
                'description': 'Modal Description',
                'end_time': datetime.now() + timedelta(hours=2),
                'winner_count': 2,
                'created_by': 555666777
            },
            'view_type': 'ModernGiveawayView'
        },
        'interactions/modals.py (quick)': {
            'data': {
                'message_id': 0,
                'guild_id': 123456789,
                'channel_id': 987654321,
                'title': '⚡ Quick: Quick Modal Prize',
                'description': 'Quick Modal Description',
                'end_time': datetime.now() + timedelta(minutes=5),
                'winner_count': 1,
                'created_by': 555666777,
                'is_quick': True,
                'requirements': 'Must be authorized'
            },
            'view_type': 'QuickGiveawayView'
        },
        'interactions/prize_selection.py (nitro)': {
            'data': {
                'message_id': 0,
                'guild_id': 123456789,
                'channel_id': 987654321,
                'title': '💎 Discord Nitro Basic',
                'description': 'Nitro Description',
                'end_time': datetime.now() + timedelta(days=1),
                'winner_count': 1,
                'created_by': 555666777
            },
            'view_type': 'ModernGiveawayView'
        }
    }
    
    all_passed = True
    results = {}
    
    for source, test_case in test_cases.items():
        print(f"\n📦 Testing: {source}")
        
        missing, extra, type_errors = validate_data_structure(
            test_case['data'], 
            test_case['view_type'], 
            source
        )
        
        passed = len(missing) == 0 and len(type_errors) == 0
        results[source] = {
            'passed': passed,
            'missing': missing,
            'extra': extra,
            'type_errors': type_errors
        }
        
        if passed:
            print(f"   ✅ PASSED - All required fields present with correct types")
            if extra:
                print(f"   ℹ️  Extra fields (not errors): {', '.join(extra)}")
        else:
            print(f"   ❌ FAILED")
            if missing:
                print(f"      Missing fields: {', '.join(missing)}")
            if type_errors:
                print(f"      Type errors: {', '.join(type_errors)}")
            all_passed = False
    
    return all_passed, results

def test_database_compatibility():
    """Test that giveaway data is compatible with database operations"""
    print("\n🗄️  Testing Database Compatibility")
    print("=" * 60)
    
    # Database create_giveaway method signature:
    # create_giveaway(message_id, guild_id, channel_id, title, description, end_time, winner_count, created_by)
    
    required_db_fields = ['message_id', 'guild_id', 'channel_id', 'title', 'description', 'end_time', 'winner_count', 'created_by']
    
    sample_giveaway_data = {
        'message_id': 123456789,
        'guild_id': 987654321,
        'channel_id': 555666777,
        'title': 'Test Prize',
        'description': 'Test Description',
        'end_time': datetime.now() + timedelta(hours=1),
        'winner_count': 1,
        'created_by': 111222333
    }
    
    print("📋 Checking database field compatibility...")
    
    missing_for_db = []
    for field in required_db_fields:
        if field not in sample_giveaway_data:
            missing_for_db.append(field)
    
    if missing_for_db:
        print(f"   ❌ Missing fields for database: {', '.join(missing_for_db)}")
        return False
    else:
        print("   ✅ All required database fields are present")
        
        # Test parameter extraction
        try:
            db_params = (
                sample_giveaway_data['message_id'],
                sample_giveaway_data['guild_id'],
                sample_giveaway_data['channel_id'],
                sample_giveaway_data['title'],
                sample_giveaway_data['description'],
                sample_giveaway_data['end_time'],
                sample_giveaway_data['winner_count'],
                sample_giveaway_data['created_by']
            )
            print(f"   ✅ Database parameters can be extracted successfully")
            print(f"   ℹ️  Parameter types: {[type(p).__name__ for p in db_params]}")
            return True
        except KeyError as e:
            print(f"   ❌ Error extracting database parameters: {e}")
            return False

def main():
    """Main validation function"""
    print("🚀 Giveaway Data Flow Validation")
    print("=" * 80)
    
    # Step 1: Analyze requirements
    requirements = analyze_giveaway_view_requirements()
    
    # Step 2: Test data structures
    structures_passed, structure_results = test_giveaway_data_structures()
    
    # Step 3: Test database compatibility
    db_passed = test_database_compatibility()
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 VALIDATION SUMMARY")
    print("=" * 80)
    
    print(f"\n🎯 Data Structure Validation: {'✅ PASSED' if structures_passed else '❌ FAILED'}")
    print(f"🗄️  Database Compatibility: {'✅ PASSED' if db_passed else '❌ FAILED'}")
    
    if structures_passed and db_passed:
        print("\n🎉 ALL VALIDATIONS PASSED!")
        print("✅ Giveaway data flow is consistent across all creation paths")
        print("✅ All required fields are properly passed to view classes")
        print("✅ Database operations will receive correct parameters")
        return 0
    else:
        print("\n❌ SOME VALIDATIONS FAILED!")
        print("⚠️  Review the errors above and fix the data flow issues")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
