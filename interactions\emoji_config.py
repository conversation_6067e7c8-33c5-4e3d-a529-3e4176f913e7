"""
Emoji configuration system for customizable prize emojis.
Allows server admins to configure custom emojis for different prize types.
"""
import discord
from typing import Dict, Optional

from core.logging import logger
from embeds.builders import create_success_embed, create_error_embed, create_info_embed
from utils.database import db_manager

class EmojiConfigModal(discord.ui.Modal):
    """Modal for configuring prize emojis"""

    def __init__(self, guild_id: int = None):
        super().__init__(title="Configure Prize Emojis")
        self.guild_id = guild_id

        # Initialize with default placeholders - will be updated in setup if guild_id provided
        self.nitro_emoji = discord.ui.TextInput(
            label="Discord Nitro Emoji",
            placeholder="Current: 💎 (Enter new emoji or leave blank to keep)",
            max_length=10,
            required=False
        )

        self.robux_emoji = discord.ui.TextInput(
            label="Robux Emoji",
            placeholder="Current: 🔶 (Enter new emoji or leave blank to keep)",
            max_length=10,
            required=False
        )

        self.garden_emoji = discord.ui.TextInput(
            label="Grow A Garden Emoji",
            placeholder="Current: 🌳 (Enter new emoji or leave blank to keep)",
            max_length=10,
            required=False
        )

        self.custom_emoji = discord.ui.TextInput(
            label="Custom Prize Emoji",
            placeholder="Current: 🎁 (Enter new emoji or leave blank to keep)",
            max_length=10,
            required=False
        )

        self.add_item(self.nitro_emoji)
        self.add_item(self.robux_emoji)
        self.add_item(self.garden_emoji)
        self.add_item(self.custom_emoji)

    @classmethod
    async def create(cls, guild_id: int):
        """Create an EmojiConfigModal with current emoji placeholders"""
        modal = cls(guild_id)
        await modal._update_placeholders()
        return modal

    async def _update_placeholders(self):
        """Update placeholders with current configured emojis"""
        if not self.guild_id:
            return

        try:
            emojis = await EmojiManager.get_guild_emojis(self.guild_id)

            # Update placeholders to show current emojis
            self.nitro_emoji.placeholder = f"Current: {emojis['nitro']} (Enter new emoji or leave blank to keep)"
            self.robux_emoji.placeholder = f"Current: {emojis['robux']} (Enter new emoji or leave blank to keep)"
            self.garden_emoji.placeholder = f"Current: {emojis['garden']} (Enter new emoji or leave blank to keep)"
            self.custom_emoji.placeholder = f"Current: {emojis['custom']} (Enter new emoji or leave blank to keep)"

        except Exception as e:
            logger.error(f"Error updating emoji placeholders: {e}")
            # Keep default placeholders on error
    
    async def on_submit(self, interaction: discord.Interaction):
        """Handle emoji configuration"""
        try:
            config_data = {}
            
            # Validate and set emojis
            if self.nitro_emoji.value:
                config_data['nitro_emoji'] = self.nitro_emoji.value.strip()
            
            if self.robux_emoji.value:
                config_data['robux_emoji'] = self.robux_emoji.value.strip()
            
            if self.garden_emoji.value:
                config_data['garden_emoji'] = self.garden_emoji.value.strip()
            
            if self.custom_emoji.value:
                config_data['custom_emoji'] = self.custom_emoji.value.strip()
            
            if config_data:
                # Save emoji configuration
                success = await db_manager.update_guild_config(interaction.guild.id, **config_data)
                
                if success:
                    embed = create_success_embed(
                        "✅ Emoji Configuration Updated",
                        "Your prize emojis have been updated successfully!"
                    )
                    
                    # Show configured emojis
                    if 'nitro_emoji' in config_data:
                        embed.add_field(
                            name="Discord Nitro", 
                            value=f"{config_data['nitro_emoji']} Discord Nitro", 
                            inline=True
                        )
                    
                    if 'robux_emoji' in config_data:
                        embed.add_field(
                            name="Robux", 
                            value=f"{config_data['robux_emoji']} Robux", 
                            inline=True
                        )
                    
                    if 'garden_emoji' in config_data:
                        embed.add_field(
                            name="Grow A Garden", 
                            value=f"{config_data['garden_emoji']} Garden Items", 
                            inline=True
                        )
                    
                    if 'custom_emoji' in config_data:
                        embed.add_field(
                            name="Custom Prize", 
                            value=f"{config_data['custom_emoji']} Custom Prizes", 
                            inline=True
                        )
                    
                    await interaction.response.send_message(embed=embed, ephemeral=True)
                    logger.info(f"Emoji configuration updated by {interaction.user} in {interaction.guild}")
                else:
                    embed = create_error_embed(
                        "Configuration Failed",
                        "Failed to save emoji configuration. Please try again."
                    )
                    await interaction.response.send_message(embed=embed, ephemeral=True)
            else:
                embed = create_error_embed(
                    "No Changes Made",
                    "Please provide at least one emoji to update."
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
                
        except Exception as e:
            logger.error(f"Error in emoji configuration: {e}")
            embed = create_error_embed(
                "Configuration Error",
                "Something went wrong. Please try again."
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)

class EmojiManager:
    """Manager for handling custom emojis"""
    
    @staticmethod
    async def get_guild_emojis(guild_id: int) -> Dict[str, str]:
        """Get configured emojis for a guild"""
        try:
            config = await db_manager.get_guild_config(guild_id)
            
            # Default emojis
            default_emojis = {
                'nitro': '💎',
                'robux': '🔶', 
                'garden': '🌳',
                'custom': '🎁'
            }
            
            if config:
                # Override with custom emojis if configured
                if config.get('nitro_emoji'):
                    default_emojis['nitro'] = config['nitro_emoji']
                if config.get('robux_emoji'):
                    default_emojis['robux'] = config['robux_emoji']
                if config.get('garden_emoji'):
                    default_emojis['garden'] = config['garden_emoji']
                if config.get('custom_emoji'):
                    default_emojis['custom'] = config['custom_emoji']
            
            return default_emojis
            
        except Exception as e:
            logger.error(f"Error getting guild emojis: {e}")
            # Return defaults on error
            return {
                'nitro': '💎',
                'robux': '🔶',
                'garden': '🌳', 
                'custom': '🎁'
            }
    
    @staticmethod
    async def format_prize(guild_id: int, prize_type: str, prize_text: str) -> str:
        """Format a prize with the appropriate emoji"""
        try:
            emojis = await EmojiManager.get_guild_emojis(guild_id)
            emoji = emojis.get(prize_type, '🎁')
            return f"{emoji} {prize_text}"
        except Exception as e:
            logger.error(f"Error formatting prize: {e}")
            return f"🎁 {prize_text}"

async def show_current_emojis(interaction: discord.Interaction):
    """Show current emoji configuration"""
    try:
        emojis = await EmojiManager.get_guild_emojis(interaction.guild.id)
        
        embed = create_info_embed(
            "🎨 Current Prize Emojis",
            f"Here are your current prize emojis:\n\n"
            f"**Discord Nitro:** {emojis['nitro']}\n"
            f"**Robux:** {emojis['robux']}\n"
            f"**Grow A Garden:** {emojis['garden']}\n"
            f"**Custom Prize:** {emojis['custom']}\n\n"
            f"Use the **Configure Emojis** button to change them!"
        )
        
        await interaction.response.send_message(embed=embed, ephemeral=True)
        
    except Exception as e:
        logger.error(f"Error showing current emojis: {e}")
        embed = create_error_embed(
            "Error",
            "Failed to load emoji configuration."
        )
        await interaction.response.send_message(embed=embed, ephemeral=True)
