# 🎉 Bot de Discord para Sorteos y Gestión de Comunidad

## 📖 Descripción

Bot de Discord avanzado especializado en sorteos (giveaways) con sistema de autorización OAuth2, optimizado para servidores con memoria limitada (1GB RAM) y funciones automáticas de gestión de comunidad.

## ✨ Características Principales

### 🎁 Sistema de Sorteos
- **Sorteos por reacciones**: Los usuarios participan reaccionando con emojis
- **Múltiples ganadores**: Configura cuántos ganadores quieres
- **Duración personalizable**: Desde minutos hasta días
- **Requisitos de roles**: Solo usuarios con roles específicos pueden participar
- **Entradas adicionales**: Usuarios con ciertos roles obtienen más oportunidades

### 🔐 Sistema de Autorización OAuth2
- **Autorización automática**: Los usuarios pueden autorizar al bot para unirse a sus servidores
- **Flujo OAuth2 completo**: Implementa Authorization Code Grant correctamente
- **Servidor de callback local**: Maneja las redirecciones OAuth2 automáticamente
- **Gestión de tokens**: Almacena y gestiona tokens de acceso de forma segura

### 👋 Funciones de Bienvenida
- **Ghost pings**: Menciona a nuevos miembros y luego elimina el mensaje
- **Mensajes personalizables**: Configura mensajes de bienvenida únicos
- **Roles automáticos**: Asigna roles a nuevos miembros

### 🎯 Recompensas y Hitos
- **Actividad aleatoria**: Recompensas sorpresa por participación
- **Hitos de miembros**: Sorteos automáticos al alcanzar cierto número de miembros
- **Sistema de puntos**: Seguimiento de participación de usuarios

### ⚡ Optimización de Memoria
- **Caché inteligente**: Minimiza el uso de memoria
- **Limpieza automática**: Garbage collection periódico
- **Conexiones eficientes**: Pool de conexiones optimizado
- **Sincronización inteligente**: Solo sincroniza comandos cuando cambian

## 🛠️ Instalación y Configuración

### 1. Requisitos del Sistema
- Python 3.8+
- 1GB RAM mínimo
- Conexión a internet estable

### 2. Instalación Rápida

```bash
# Clonar o descargar el proyecto
cd WorkHidden

# Instalar dependencias
pip install -r requirements.txt

# Configurar variables de entorno
cp .env.example .env
# Editar .env con tus credenciales

# Verificar configuración
python test_bot.py

# Ejecutar el bot
python main.py
```

### 3. Configuración de Discord

#### A. Crear Aplicación
1. Ve al [Portal de Desarrolladores de Discord](https://discord.com/developers/applications)
2. Crea una nueva aplicación
3. Guarda el **Application ID**

#### B. Configurar Bot
1. Ve a la pestaña "Bot"
2. Crea el bot y copia el **Token**
3. **IMPORTANTE**: Habilita todos los intents privilegiados:
   - ✅ Presence Intent
   - ✅ Server Members Intent
   - ✅ Message Content Intent

#### C. Configurar OAuth2
1. Ve a "OAuth2" → "General"
2. Copia el **Client Secret**
3. En "Redirects", añade: `http://localhost:8080/callback`
4. Activa temporalmente "Public Bot" y "Guild Install"

### 4. Variables de Entorno

Configura tu archivo `.env`:

```env
# Requerido
DISCORD_TOKEN=tu_token_del_bot
APPLICATION_ID=tu_application_id
DISCORD_CLIENT_SECRET=tu_client_secret
REDIRECT_URI=http://localhost:8080/callback

# Opcional
OAUTH_PORT=8080
LOG_LEVEL=INFO
```

## 🚀 Uso del Bot

### Comandos Principales

#### Sorteos
- `/giveaway create` - Crear un nuevo sorteo
- `/giveaway end <id>` - Finalizar un sorteo
- `/giveaway list` - Ver sorteos activos
- `/giveaway reroll <id>` - Volver a sortear ganadores

#### Autorización
- `/authorize` - Autorizar funciones avanzadas del bot
- `/auth status` - Ver estado de autorización
- `/auth revoke` - Revocar autorización

#### Configuración
- `/config welcome` - Configurar mensajes de bienvenida
- `/config roles` - Configurar roles automáticos
- `/config giveaway` - Configurar opciones de sorteos

#### Administración
- `/admin stats` - Estadísticas del servidor
- `/admin cleanup` - Limpiar mensajes antiguos
- `/admin backup` - Respaldar configuración

### Ejemplo de Uso

1. **Crear un sorteo básico**:
   ```
   /giveaway create
   Premio: Nintendo Switch
   Duración: 1d
   Ganadores: 1
   ```

2. **Sorteo con requisitos**:
   ```
   /giveaway create
   Premio: Nitro Discord
   Duración: 12h
   Ganadores: 3
   Rol requerido: @Miembro Activo
   Entradas extra: @VIP (2x)
   ```

3. **Autorizar bot para auto-join**:
   ```
   /authorize
   # Sigue el enlace OAuth2
   # Autoriza los permisos
   # ¡Listo! El bot puede unirse automáticamente
   ```

## 🔧 Solución de Problemas

### Error: `PrivilegedIntentsRequired`
**Solución**: Habilita todos los intents privilegiados en Discord Developer Portal

### Error: `unsupported_response_type`
**Causa**: Configuración OAuth2 incorrecta
**Solución**: 
1. Verifica que `DISCORD_CLIENT_SECRET` esté configurado
2. Activa "Public Bot" temporalmente
3. Marca "Guild Install" en OAuth2 → Installation
4. Usa `response_type=code` (ya implementado)

### Error: `Invalid OAuth2 access token`
**Solución**: 
1. Regenera el Client Secret
2. Verifica que el scope `guilds.join` esté incluido
3. Reautoriza el bot

### Bot no responde
**Solución**:
1. Verifica el token del bot
2. Comprueba que el bot tenga permisos en el servidor
3. Revisa los logs para errores específicos

### Problemas de memoria
**Solución**:
1. El bot está optimizado para 1GB RAM
2. Reinicia el bot si el uso de memoria es alto
3. Ajusta `max_cached_users` en la configuración

## 📁 Estructura del Proyecto

```
WorkHidden/
├── core/
│   └── bot.py              # Bot principal optimizado
├── config/
│   └── messages.py         # Configuración OAuth2 y mensajes
├── utils/
│   ├── oauth_server.py     # Servidor OAuth2 callback
│   └── database.py         # Gestión de base de datos
├── commands/
│   ├── giveaway.py         # Comandos de sorteos
│   ├── admin.py            # Comandos de administración
│   └── slash_commands.py   # Comandos slash
├── events/
│   └── member_events.py    # Eventos de miembros
├── interactions/
│   └── authorization_views.py # Vistas de autorización
├── main.py                 # Punto de entrada
├── test_bot.py            # Script de verificación
├── requirements.txt        # Dependencias
├── .env.example           # Plantilla de configuración
├── SETUP_GUIDE.md         # Guía de configuración
└── README.md              # Este archivo
```

## 🔒 Seguridad

- **Tokens seguros**: Nunca hardcodees tokens en el código
- **Variables de entorno**: Usa `.env` para credenciales
- **Permisos mínimos**: El bot solo solicita permisos necesarios
- **Validación de entrada**: Todas las entradas de usuario son validadas
- **Logs seguros**: No se registran tokens ni datos sensibles

## 🤝 Contribuir

1. Fork el proyecto
2. Crea una rama para tu feature (`git checkout -b feature/AmazingFeature`)
3. Commit tus cambios (`git commit -m 'Add some AmazingFeature'`)
4. Push a la rama (`git push origin feature/AmazingFeature`)
5. Abre un Pull Request

## 📄 Licencia

Este proyecto está bajo la Licencia MIT. Ver `LICENSE` para más detalles.

## 📞 Soporte

- **Documentación**: Consulta `SETUP_GUIDE.md` para configuración detallada
- **Verificación**: Ejecuta `python test_bot.py` para diagnosticar problemas
- **Logs**: Revisa los logs del bot para errores específicos
- **Discord.py**: [Documentación oficial](https://discordpy.readthedocs.io/)

## 🎯 Roadmap

- [ ] Panel web de administración
- [ ] Integración con APIs externas
- [ ] Sistema de economía virtual
- [ ] Comandos de moderación avanzados
- [ ] Análisis de actividad del servidor
- [ ] Soporte para múltiples idiomas

---

**¡Disfruta usando tu bot de Discord! 🎉**

> **Nota**: Este bot está optimizado para servidores con recursos limitados y implementa las mejores prácticas de Discord OAuth2 para evitar errores comunes como `unsupported_response_type`.