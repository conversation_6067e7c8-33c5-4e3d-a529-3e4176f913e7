"""OAuth2 callback server for Discord authorization flow"""

import asyncio
import aiohttp
from aiohttp import web, ClientSession
import urllib.parse
import logging
import os
import socket
import json
import time
from pathlib import Path
from typing import Optional, Dict, Any, List
import discord

from core.bot import bot

logger = logging.getLogger(__name__)

class PortManager:
    """Manages port discovery and fallback for OAuth server"""
    
    DEFAULT_PORTS = [8080, 8081, 8082, 8083, 3000, 3001, 5000, 5001, 9000, 9001]
    
    @staticmethod
    def is_port_available(port: int, host: str = 'localhost') -> bool:
        """Check if a port is available"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(1)
                result = sock.connect_ex((host, port))
                return result != 0
        except Exception as e:
            logger.warning(f"Error checking port {port}: {e}")
            return False
    
    @staticmethod
    def find_available_port(preferred_port: int = 8080, host: str = 'localhost') -> Optional[int]:
        """Find an available port, starting with preferred port"""
        # Try preferred port first
        if PortManager.is_port_available(preferred_port, host):
            return preferred_port
        
        # Try other common ports
        for port in PortManager.DEFAULT_PORTS:
            if port != preferred_port and PortManager.is_port_available(port, host):
                logger.info(f"Port {preferred_port} unavailable, using fallback port {port}")
                return port
        
        # Try dynamic range as last resort
        for port in range(8000, 9000):
            if PortManager.is_port_available(port, host):
                logger.info(f"Using dynamic port {port}")
                return port
        
        return None

class AuthorizationDataManager:
    """Manages persistent storage and recovery of OAuth authorizations"""
    
    def __init__(self, data_file: str = "data/oauth_authorizations.json"):
        self.data_file = Path(data_file)
        self.data_file.parent.mkdir(exist_ok=True)
    
    def save_authorizations(self, authorizations: Dict[str, Any]) -> bool:
        """Save authorizations to persistent storage"""
        try:
            # Create backup of existing data
            if self.data_file.exists():
                backup_file = self.data_file.with_suffix('.json.backup')
                import shutil
                shutil.copy2(self.data_file, backup_file)
            
            # Save new data
            with open(self.data_file, 'w') as f:
                json.dump(authorizations, f, indent=2, default=str)
            
            logger.debug(f"Saved {len(authorizations)} authorizations to {self.data_file}")
            return True
        except Exception as e:
            logger.error(f"Failed to save authorizations: {e}")
            return False
    
    def load_authorizations(self) -> Dict[str, Any]:
        """Load authorizations from persistent storage"""
        try:
            if not self.data_file.exists():
                return {}
            
            with open(self.data_file, 'r') as f:
                data = json.load(f)
            
            logger.info(f"Loaded {len(data)} authorizations from {self.data_file}")
            return data
        except Exception as e:
            logger.error(f"Failed to load authorizations: {e}")
            
            # Try backup file
            backup_file = self.data_file.with_suffix('.json.backup')
            if backup_file.exists():
                try:
                    with open(backup_file, 'r') as f:
                        data = json.load(f)
                    logger.info(f"Restored {len(data)} authorizations from backup")
                    return data
                except Exception as backup_error:
                    logger.error(f"Backup recovery also failed: {backup_error}")
            
            return {}
    
    def cleanup_expired_authorizations(self, authorizations: Dict[str, Any], max_age_hours: int = 24) -> Dict[str, Any]:
        """Remove expired authorizations to prevent memory leaks"""
        try:
            current_time = time.time()
            cleaned_data = {}
            removed_count = 0
            
            for user_id, auth_data in authorizations.items():
                # Check if authorization has timestamp
                auth_time = auth_data.get('timestamp', current_time)
                age_hours = (current_time - auth_time) / 3600
                
                if age_hours <= max_age_hours:
                    cleaned_data[user_id] = auth_data
                else:
                    removed_count += 1
            
            if removed_count > 0:
                logger.info(f"Cleaned up {removed_count} expired authorizations")
            
            return cleaned_data
        except Exception as e:
            logger.error(f"Error during authorization cleanup: {e}")
            return authorizations

class OAuth2Server:
    """Local server to handle Discord OAuth2 callbacks with robust error handling"""
    
    def __init__(self, client_id: str, client_secret: str, redirect_uri: str = "http://localhost:8080/callback"):
        self.client_id = client_id
        self.client_secret = client_secret
        self.redirect_uri = redirect_uri
        self.app = web.Application()
        self.runner = None
        self.site = None
        self.current_port = None
        self.current_host = None
        
        # Initialize data manager for persistent storage
        self.data_manager = AuthorizationDataManager()
        
        # Load existing authorizations from persistent storage
        self.pending_authorizations = self.data_manager.load_authorizations()
        
        # Clean up expired authorizations
        self.pending_authorizations = self.data_manager.cleanup_expired_authorizations(
            self.pending_authorizations
        )
        
        # Setup routes
        self.app.router.add_get('/callback', self.handle_callback)
        self.app.router.add_get('/status/{user_id}', self.check_auth_status)
        self.app.router.add_get('/health', self.health_check)
        
        logger.info(f"OAuth2Server initialized with {len(self.pending_authorizations)} existing authorizations")
        
    async def start_server(self, host='localhost', preferred_port=8080, max_retries=3):
        """Start the OAuth callback server with automatic port fallback"""
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                # Find available port using PortManager
                if retry_count == 0:
                    # First try: use preferred port
                    available_port = PortManager.find_available_port(preferred_port, host)
                else:
                    # Retry: skip the previously tried port
                    available_port = PortManager.find_available_port(preferred_port + retry_count, host)
                
                if not available_port:
                    logger.error("No available ports found for OAuth2 server")
                    return False
                
                # Update redirect URI if port changed
                if available_port != preferred_port:
                    old_redirect = self.redirect_uri
                    self.redirect_uri = self.redirect_uri.replace(f":{preferred_port}/", f":{available_port}/")
                    logger.info(f"Updated redirect URI from {old_redirect} to {self.redirect_uri}")
                
                # Start the server
                self.runner = web.AppRunner(self.app)
                await self.runner.setup()
                self.site = web.TCPSite(self.runner, host, available_port)
                await self.site.start()
                
                self.current_host = host
                self.current_port = available_port
                
                logger.info(f"✅ OAuth2 callback server started on http://{host}:{available_port}")
                logger.info(f"📱 Users can authorize at: {self.redirect_uri}")
                
                if available_port != preferred_port:
                    logger.warning(f"⚠️  Using fallback port {available_port} instead of {preferred_port}")
                    logger.info(f"💡 Update your Discord Developer Portal redirect URI to: {self.redirect_uri}")
                
                return True
                
            except Exception as e:
                retry_count += 1
                logger.warning(f"Failed to start OAuth2 server (attempt {retry_count}/{max_retries}): {e}")
                
                if retry_count < max_retries:
                    logger.info(f"Retrying in 2 seconds...")
                    await asyncio.sleep(2)
                else:
                    logger.error("All retry attempts exhausted")
                    return False
                
                # Clean up failed attempt
                try:
                    if self.site:
                        await self.site.stop()
                    if self.runner:
                        await self.runner.cleanup()
                except:
                    pass
        
        return False
    
    async def stop_server(self):
        """Stop the OAuth callback server and save data"""
        try:
            # Save authorizations before stopping
            if hasattr(self, 'pending_authorizations'):
                self.data_manager.save_authorizations(self.pending_authorizations)
            
            # Stop server components
            if self.site:
                await self.site.stop()
                logger.debug("OAuth2 server site stopped")
            
            if self.runner:
                await self.runner.cleanup()
                logger.debug("OAuth2 server runner cleaned up")
            
            logger.info("✅ OAuth2 callback server stopped gracefully")
            
        except Exception as e:
            logger.error(f"Error during OAuth2 server shutdown: {e}")
            # Try force cleanup
            try:
                if self.runner:
                    await self.runner.cleanup()
            except:
                pass
    
    async def handle_callback(self, request):
        """Handle OAuth2 callback from Discord"""
        try:
            # Get authorization code from query parameters
            code = request.query.get('code')
            state = request.query.get('state')  # Can be used to track guild_id or user_id
            error = request.query.get('error')
            
            if error:
                logger.warning(f"OAuth2 error: {error}")
                return web.Response(
                    text=f"<html><body><h1>Authorization Failed</h1><p>Error: {error}</p></body></html>",
                    content_type='text/html',
                    status=400
                )
            
            if not code:
                logger.warning("No authorization code received")
                return web.Response(
                    text="<html><body><h1>Authorization Failed</h1><p>No authorization code received</p></body></html>",
                    content_type='text/html',
                    status=400
                )
            
            # Exchange code for access token
            token_data = await self.exchange_code_for_token(code)
            
            if token_data:
                # Get user info using the access token
                user_info = await self.get_user_info(token_data['access_token'])
                
                if user_info:
                    user_id = user_info['id']
                    username = f"{user_info['username']}#{user_info['discriminator']}" if user_info.get('discriminator') != '0' else user_info['username']
                    
                    # Store comprehensive authorization data
                    auth_data = {
                        'user_id': user_id,
                        'username': username,
                        'access_token': token_data['access_token'],
                        'refresh_token': token_data.get('refresh_token'),
                        'expires_in': token_data.get('expires_in', 604800),  # Default 7 days
                        'token_type': token_data.get('token_type', 'Bearer'),
                        'scope': token_data.get('scope', ''),
                        'authorized': True,
                        'authorized_at': time.time(),
                        'user_info': user_info
                    }
                    
                    # Store in OAuth server memory for immediate access
                    self.pending_authorizations[user_id] = auth_data
                    
                    # Store in database for persistence (CRITICAL FIX)
                    try:
                        from utils.database import db_manager
                        await db_manager.add_authorized_user(
                            user_id=int(user_id),
                            username=username,
                            access_token=token_data['access_token'],
                            refresh_token=token_data.get('refresh_token'),
                            expires_in=token_data.get('expires_in', 604800)
                        )
                        logger.info(f"✅ Successfully stored OAuth tokens for user {username} ({user_id})")
                    except Exception as e:
                        logger.error(f"❌ Failed to store OAuth tokens in database: {e}")
                    
                    # Store in JSON file for backup
                    self.data_manager.save_authorizations(self.pending_authorizations)
                    
                    logger.info(f"✅ OAuth authorization successful for {username} ({user_id})")
                    
                    # NOTE: Automatic server joining removed - admins will use dashboard "Add Members" functionality
                    logger.debug(f"OAuth tokens stored for {username} - ready for manual member addition via dashboard")
                    
                    return web.Response(
                        text=f"""
                        <html>
                            <head>
                                <title>Authorization Successful</title>
                                <style>
                                    body {{ font-family: Arial, sans-serif; text-align: center; margin-top: 50px; }}
                                    .success {{ color: #2ecc71; font-size: 24px; margin-bottom: 20px; }}
                                    .info {{ color: #34495e; font-size: 16px; }}
                                </style>
                            </head>
                            <body>
                                <div class="success">✅ Authorization Successful!</div>
                                <div class="info">
                                    <p><strong>Welcome {username}!</strong></p>
                                    <p>You are now authorized to participate in giveaways.</p>
                                    <p>You can safely close this window and return to Discord.</p>
                                </div>
                            </body>
                        </html>
                        """,
                        content_type='text/html'
                    )
                else:
                    logger.error("Failed to get user info from Discord API")
                    return web.Response(
                        text="<html><body><h1>Authorization Failed</h1><p>Could not retrieve user information</p></body></html>",
                        content_type='text/html',
                        status=400
                    )
            else:
                logger.error("Failed to exchange authorization code for token")
                return web.Response(
                    text="<html><body><h1>Authorization Failed</h1><p>Token exchange failed</p></body></html>",
                    content_type='text/html',
                    status=400
                )
        except Exception as e:
            logger.error(f"Error handling OAuth2 callback: {e}")
            return web.Response(
                text="<html><body><h1>Authorization Failed</h1><p>Internal server error</p></body></html>",
                content_type='text/html',
                status=500
            )
    
    async def exchange_code_for_token(self, code: str) -> Optional[Dict[str, Any]]:
        """Exchange authorization code for access token"""
        try:
            # Prepare token exchange data
            data = {
                'client_id': self.client_id,
                'client_secret': self.client_secret,
                'grant_type': 'authorization_code',
                'code': code,
                'redirect_uri': self.redirect_uri
            }
            
            # Make token exchange request
            async with ClientSession() as session:
                async with session.post(
                    'https://discord.com/api/oauth2/token',
                    data=data,
                    headers={
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'Accept': 'application/json'
                    }
                ) as response:
                    if response.status == 200:
                        token_data = await response.json()
                        logger.info("Successfully exchanged code for token")
                        return token_data
                    else:
                        error_text = await response.text()
                        logger.error(f"Token exchange failed: {response.status} - {error_text}")
                        return None
                        
        except Exception as e:
            logger.error(f"Error exchanging code for token: {e}")
            return None
    
    async def get_user_info(self, access_token: str) -> Optional[Dict[str, Any]]:
        """Get user information using access token"""
        try:
            async with ClientSession() as session:
                async with session.get(
                    'https://discord.com/api/users/@me',
                    headers={
                        'Authorization': f'Bearer {access_token}',
                        'Accept': 'application/json'
                    }
                ) as response:
                    if response.status == 200:
                        user_data = await response.json()
                        return user_data
                    else:
                        error_text = await response.text()
                        logger.error(f"Failed to get user info: {response.status} - {error_text}")
                        return None
                        
        except Exception as e:
            logger.error(f"Error getting user info: {e}")
            return None
    
    async def check_auth_status(self, request):
        """Check authorization status for a user"""
        user_id = request.match_info['user_id']
        
        if user_id in self.pending_authorizations:
            auth_data = self.pending_authorizations[user_id]
            return web.json_response({
                'authorized': auth_data['authorized'],
                'username': auth_data['user_info']['username']
            })
        else:
            return web.json_response({
                'authorized': False
            })
    
    def is_user_authorized(self, user_id: str) -> bool:
        """Check if a user has completed OAuth2 authorization"""
        return user_id in self.pending_authorizations and self.pending_authorizations[user_id]['authorized']
    
    def get_user_token(self, user_id: str) -> Optional[str]:
        """Get access token for a user"""
        if user_id in self.pending_authorizations:
            return self.pending_authorizations[user_id]['access_token']
        return None
    
    def clear_user_authorization(self, user_id: str):
        """Clear stored authorization for a user"""
        if user_id in self.pending_authorizations:
            del self.pending_authorizations[user_id]
            # Update persistent storage
            self.data_manager.save_authorizations(self.pending_authorizations)
            logger.debug(f"Cleared authorization for user {user_id}")
    
    async def health_check(self, request):
        """Health check endpoint for monitoring"""
        try:
            health_data = {
                'status': 'healthy',
                'server_info': {
                    'host': self.current_host,
                    'port': self.current_port,
                    'redirect_uri': self.redirect_uri
                },
                'authorizations': {
                    'total': len(self.pending_authorizations),
                    'recent': len([
                        auth for auth in self.pending_authorizations.values()
                        if time.time() - auth.get('timestamp', 0) < 3600  # Last hour
                    ])
                },
                'timestamp': time.time(),
                'uptime_seconds': time.time() - getattr(self, 'start_time', time.time())
            }
            
            return web.json_response(health_data)
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return web.json_response({
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': time.time()
            }, status=500)
    
    def get_server_info(self) -> Dict[str, Any]:
        """Get current server information"""
        return {
            'host': self.current_host,
            'port': self.current_port,
            'redirect_uri': self.redirect_uri,
            'is_running': self.site is not None,
            'authorizations_count': len(self.pending_authorizations)
        }
    
    async def _auto_join_partner_servers(self, user_id: str, access_token: str, guild_id: int) -> List[Dict[str, Any]]:
        """Automatically add user to configured partner servers after OAuth authorization"""
        results = []
        
        try:
            from utils.database import db_manager
            from core.bot import bot
            
            # Get partner servers for this guild
            partner_servers = await db_manager.get_partner_servers(guild_id)
            
            if not partner_servers:
                logger.debug(f"No partner servers configured for guild {guild_id}")
                return results
            
            # Get auto-join configuration
            auto_join_config = await db_manager.get_auto_join_servers(guild_id)
            
            logger.info(f"Starting auto-join process for user {user_id} to {len(partner_servers)} partner servers")
            
            for server_id in partner_servers:
                try:
                    # Check if auto-join is enabled for this server
                    server_config = auto_join_config.get(str(server_id), {})
                    if not server_config.get('enabled', False):
                        logger.debug(f"Auto-join disabled for server {server_id}, skipping")
                        results.append({
                            'server_id': server_id,
                            'success': False,
                            'reason': 'Auto-join disabled for this server',
                            'action': 'skipped'
                        })
                        continue
                    
                    # Get the target guild
                    target_guild = bot.get_guild(server_id)
                    if not target_guild:
                        logger.warning(f"Bot not in partner server {server_id}")
                        results.append({
                            'server_id': server_id,
                            'success': False,
                            'reason': 'Bot not in target server',
                            'action': 'failed'
                        })
                        continue
                    
                    # Check if user is already in the server
                    try:
                        await target_guild.fetch_member(int(user_id))
                        logger.debug(f"User {user_id} already in server {target_guild.name}")
                        results.append({
                            'server_id': server_id,
                            'server_name': target_guild.name,
                            'success': True,
                            'reason': 'User already in server',
                            'action': 'already_member'
                        })
                        continue
                    except discord.NotFound:
                        # User not in server, proceed with adding
                        pass
                    
                    # Try to add user to the server using OAuth token via Discord API
                    try:
                        user = await bot.fetch_user(int(user_id))

                        # Discord API endpoint for adding guild member
                        import aiohttp
                        url = f"https://discord.com/api/v10/guilds/{target_guild.id}/members/{user.id}"
                        headers = {
                            "Authorization": f"Bot {bot.http.token}",
                            "Content-Type": "application/json"
                        }
                        data = {
                            "access_token": access_token
                        }

                        async with aiohttp.ClientSession() as session:
                            async with session.put(url, headers=headers, json=data) as response:
                                if response.status in [200, 201]:
                                    logger.info(f"Successfully auto-joined user {user_id} to {target_guild.name}")
                                    results.append({
                                        'server_id': server_id,
                                        'server_name': target_guild.name,
                                        'success': True,
                                        'reason': 'Successfully added to server',
                                        'action': 'added'
                                    })

                                    # Send notification DM to user
                                    try:
                                        await user.send(
                                            f'🎉 **Welcome to {target_guild.name}!**\n\n'
                                            f'You have been automatically added to this partner server as part of your authorization.\n\n'
                                            f'**What you can now access:**\n'
                                            f'• Special events and giveaways\n'
                                            f'• Community discussions\n'
                                            f'• Member-only channels and features\n\n'
                                            f'Welcome to the community network! 🚀'
                                        )
                                    except Exception as dm_error:
                                        logger.debug(f"Could not send DM to user {user_id}: {dm_error}")

                                elif response.status == 403:
                                    logger.warning(f"OAuth token invalid or insufficient permissions for user {user_id}")
                                    results.append({
                                        'server_id': server_id,
                                        'server_name': target_guild.name,
                                        'success': False,
                                        'reason': 'OAuth token invalid or insufficient permissions',
                                        'action': 'failed'
                                    })
                                else:
                                    error_text = await response.text()
                                    logger.error(f"API error adding user {user_id} to {target_guild.name}: {response.status} - {error_text}")
                                    results.append({
                                        'server_id': server_id,
                                        'server_name': target_guild.name,
                                        'success': False,
                                        'reason': f'API error: {response.status}',
                                        'action': 'failed'
                                    })
                    
                    except Exception as e:
                        logger.error(f"Unexpected error adding user {user_id} to server {server_id}: {e}")
                        results.append({
                            'server_id': server_id,
                            'server_name': target_guild.name if target_guild else 'Unknown',
                            'success': False,
                            'reason': f'Unexpected error: {str(e)}',
                            'action': 'failed'
                        })
                    
                    # Rate limiting - wait between server additions
                    await asyncio.sleep(1)
                    
                except Exception as server_error:
                    logger.error(f"Error processing server {server_id} for user {user_id}: {server_error}")
                    results.append({
                        'server_id': server_id,
                        'success': False,
                        'reason': f'Processing error: {str(server_error)}',
                        'action': 'failed'
                    })
            
            # Log summary
            successful = len([r for r in results if r['success']])
            total = len(results)
            logger.info(f"Auto-join completed for user {user_id}: {successful}/{total} successful")
            
        except Exception as e:
            logger.error(f"Error in auto-join process for user {user_id}: {e}")
            results.append({
                'server_id': None,
                'success': False,
                'reason': f'Auto-join process error: {str(e)}',
                'action': 'failed'
            })
        
        return results
    
    async def trigger_auto_join_for_user(self, user_id: str, guild_id: int) -> List[Dict[str, Any]]:
        """Manually trigger auto-join for a user (for testing/admin purposes)"""
        try:
            # Check if user is authorized and has valid token
            if user_id not in self.pending_authorizations:
                logger.warning(f"User {user_id} not found in pending authorizations")
                return [{
                    'server_id': None,
                    'success': False,
                    'reason': 'User not found in OAuth authorizations',
                    'action': 'failed'
                }]
            
            auth_data = self.pending_authorizations[user_id]
            access_token = auth_data.get('access_token')
            
            if not access_token:
                logger.warning(f"No access token found for user {user_id}")
                return [{
                    'server_id': None,
                    'success': False,
                    'reason': 'No access token available',
                    'action': 'failed'
                }]
            
            # Trigger auto-join
            results = await self._auto_join_partner_servers(user_id, access_token, guild_id)
            logger.info(f"Manual auto-join triggered for user {user_id} in guild {guild_id}")
            return results
            
        except Exception as e:
            logger.error(f"Error in manual auto-join trigger: {e}")
            return [{
                'server_id': None,
                'success': False,
                'reason': f'Manual trigger error: {str(e)}',
                'action': 'failed'
            }]

# Global OAuth2 server instance
oauth_server = None

async def start_oauth_server():
    """Start the global OAuth2 server with robust error handling"""
    global oauth_server
    
    try:
        # Get configuration from environment or config
        from config.messages import AuthorizationConfig
        
        client_id = AuthorizationConfig.CLIENT_ID
        client_secret = os.getenv('DISCORD_CLIENT_SECRET')
        redirect_uri = os.getenv('REDIRECT_URI', 'http://localhost:8080/callback')
        host = os.getenv('OAUTH_HOST', 'localhost')
        preferred_port = int(os.getenv('OAUTH_PORT', '8080'))
        
        # Validation checks with better error handling
        if not client_secret:
            logger.warning("🔧 DISCORD_CLIENT_SECRET not found in environment.")
            logger.warning("📝 OAuth2 server will not start. Please set DISCORD_CLIENT_SECRET in your .env file.")
            logger.info("💡 To fix this: Copy .env.example to .env and add your Discord Client Secret")
            logger.warning("⚠️  Authorization will use fallback verification until OAuth server is configured")
            return False
        
        if client_secret.startswith('your_') or 'here' in client_secret.lower():
            logger.warning("🔧 DISCORD_CLIENT_SECRET appears to be a placeholder value.")
            logger.warning("📝 Please update your .env file with your actual Discord Client Secret.")
            logger.warning("⚠️  Authorization will use fallback verification until OAuth server is configured")
            return False
        
        # Initialize OAuth server with robust configuration
        logger.info(f"🚀 Initializing OAuth2 server...")
        logger.info(f"📍 Preferred host: {host}:{preferred_port}")
        logger.info(f"🔗 Initial redirect URI: {redirect_uri}")
        
        oauth_server = OAuth2Server(client_id, client_secret, redirect_uri)
        oauth_server.start_time = time.time()  # Track start time for uptime
        
        # Start server with automatic port fallback
        success = await oauth_server.start_server(host, preferred_port)
        
        if success:
            server_info = oauth_server.get_server_info()
            logger.info("✅ OAuth2 authorization server is running!")
            logger.info(f"🌐 Server: http://{server_info['host']}:{server_info['port']}")
            logger.info(f"📱 Authorization URL: {server_info['redirect_uri']}")
            logger.info(f"💾 Loaded {server_info['authorizations_count']} existing authorizations")
            
            # Warn if port changed
            if server_info['port'] != preferred_port:
                logger.warning(f"⚠️  Using fallback port {server_info['port']} instead of {preferred_port}")
                logger.info(f"💡 Update Discord Developer Portal redirect URI to: {server_info['redirect_uri']}")
                
        else:
            logger.error("❌ Failed to start OAuth2 server after all retry attempts")
            logger.error("🔍 Check if all ports are in use or if you have permission issues")
            logger.warning("⚠️  Authorization will use fallback verification until OAuth server is fixed")
        
        return success
        
    except Exception as e:
        logger.error(f"💥 Unexpected error starting OAuth2 server: {e}")
        logger.warning("⚠️  Authorization will use fallback verification until OAuth server is fixed")
        return False

async def stop_oauth_server():
    """Stop the global OAuth2 server"""
    global oauth_server
    if oauth_server:
        await oauth_server.stop_server()
        oauth_server = None

def get_oauth_server() -> Optional[OAuth2Server]:
    """Get the global OAuth2 server instance"""
    return oauth_server

async def restart_oauth_server():
    """Restart the OAuth2 server (useful for error recovery)"""
    global oauth_server
    
    logger.info("🔄 Restarting OAuth2 server...")
    
    # Stop existing server if running
    if oauth_server:
        await stop_oauth_server()
        await asyncio.sleep(1)  # Brief pause for cleanup
    
    # Start new server
    success = await start_oauth_server()
    
    if success:
        logger.info("✅ OAuth2 server restarted successfully")
    else:
        logger.error("❌ Failed to restart OAuth2 server")
    
    return success

async def get_oauth_server_status() -> Dict[str, Any]:
    """Get comprehensive OAuth server status"""
    global oauth_server
    
    if not oauth_server:
        return {
            'status': 'not_running',
            'message': 'OAuth2 server is not initialized',
            'suggestions': [
                'Check if DISCORD_CLIENT_SECRET is configured',
                'Restart the bot to initialize OAuth server',
                'Check logs for startup errors'
            ]
        }
    
    try:
        server_info = oauth_server.get_server_info()
        
        # Test if server is actually responding
        is_healthy = False
        try:
            import aiohttp
            async with aiohttp.ClientSession() as session:
                health_url = f"http://{server_info['host']}:{server_info['port']}/health"
                async with session.get(health_url, timeout=5) as response:
                    is_healthy = response.status == 200
        except Exception as e:
            logger.debug(f"Health check failed: {e}")
        
        return {
            'status': 'running' if is_healthy else 'unhealthy',
            'server_info': server_info,
            'health_url': f"http://{server_info['host']}:{server_info['port']}/health",
            'is_responding': is_healthy,
            'suggestions': [
                f"Test authorization at: {server_info['redirect_uri']}",
                f"Check health at: http://{server_info['host']}:{server_info['port']}/health",
                'Use /test_oauth to verify authorization flow'
            ] if is_healthy else [
                'Server may be unresponsive - try restarting',
                'Check if port is still available',
                'Check bot logs for errors'
            ]
        }
        
    except Exception as e:
        return {
            'status': 'error',
            'error': str(e),
            'message': 'Error checking OAuth server status',
            'suggestions': [
                'Restart the OAuth server',
                'Check bot logs for detailed errors',
                'Verify environment configuration'
            ]
        }