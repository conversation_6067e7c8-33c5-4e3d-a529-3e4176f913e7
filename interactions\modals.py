"""
Modal forms for bot configuration and giveaway creation.
Provides user-friendly input forms for all bot features.
"""
import discord
import asyncio
from typing import Optional
from datetime import datetime, timedelta
import random

from core.bot import bot
from core.logging import logger
from embeds.builders import create_success_embed, create_error_embed, create_info_embed, GiveawayEmbed
from utils.database import db_manager

class GiveawayModal(discord.ui.Modal):
    """Modal for creating giveaways"""
    
    def __init__(self, giveaway_type: str = "regular"):
        super().__init__(title=f"Create {giveaway_type.title()} Giveaway")
        self.giveaway_type = giveaway_type
        
        # Add input fields
        self.prize = discord.ui.TextInput(
            label="Prize",
            placeholder="Enter prize description (e.g., Discord Nitro, $50 Robux)",
            max_length=200,
            required=True
        )
        
        self.duration = discord.ui.TextInput(
            label="Duration",
            placeholder="Enter duration (e.g., 1h, 30m, 2d, 1w)",
            max_length=10,
            required=True
        )
        
        self.winners = discord.ui.TextInput(
            label="Number of Winners",
            placeholder="How many winners? (1-20)",
            max_length=2,
            required=True
        )
        
        self.description = discord.ui.TextInput(
            label="Description (Optional)",
            placeholder="Additional details about the giveaway...",
            style=discord.TextStyle.paragraph,
            max_length=500,
            required=False
        )
        
        # Add all inputs to modal
        self.add_item(self.prize)
        self.add_item(self.duration)
        self.add_item(self.winners)
        self.add_item(self.description)
    
    async def on_submit(self, interaction: discord.Interaction):
        """Handle giveaway creation"""
        try:
            # Parse duration
            time_units = {'s': 1, 'm': 60, 'h': 3600, 'd': 86400, 'w': 604800}
            duration_seconds = 0
            
            duration_str = self.duration.value.lower()
            try:
                if duration_str[-1] in time_units:
                    duration_seconds = int(duration_str[:-1]) * time_units[duration_str[-1]]
                else:
                    duration_seconds = int(duration_str) * 60  # Default to minutes
            except (ValueError, IndexError):
                embed = create_error_embed(
                    "Invalid Duration",
                    "Please use format: 30s, 5m, 2h, 1d, 1w"
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
                return
            
            if duration_seconds < 10:
                embed = create_error_embed(
                    "Duration Too Short",
                    "Minimum duration is 10 seconds."
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
                return
            
            # Parse winners
            try:
                winner_count = int(self.winners.value)
                if winner_count < 1 or winner_count > 20:
                    raise ValueError()
            except ValueError:
                embed = create_error_embed(
                    "Invalid Winner Count",
                    "Please enter a number between 1 and 20."
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
                return
            
            # Create giveaway
            end_time = datetime.now() + timedelta(seconds=duration_seconds)
            
            # Get bonus role multipliers
            role_multipliers = await db_manager.get_role_bonus_multipliers(interaction.guild.id)
            multipliers_list = []
            if role_multipliers:
                for role_id, multiplier in role_multipliers.items():
                    role = interaction.guild.get_role(int(role_id))
                    if role:
                        multipliers_list.append(f"{role.mention}: +{multiplier} entries")
            
            # Calculate duration text
            # duration_seconds is already calculated above from user input
            duration_days = duration_seconds // 86400
            duration_hours = (duration_seconds % 86400) // 3600
            duration_minutes = (duration_seconds % 3600) // 60
            
            if duration_days > 0:
                duration_text = f"{duration_days} day{'s' if duration_days != 1 else ''}"
                duration_text += f" (Ends in {duration_days} day{'s' if duration_days != 1 else ''})"
            elif duration_hours > 0:
                duration_text = f"{duration_hours} hour{'s' if duration_hours != 1 else ''}"
                duration_text += f" (Ends in {duration_hours} hour{'s' if duration_hours != 1 else ''})"
            else:
                duration_text = f"{duration_minutes} minute{'s' if duration_minutes != 1 else ''} (Ends in {duration_minutes}m)"

            # Generate unique giveaway ID
            import time
            giveaway_id = str(int(time.time() * 1000))  # Timestamp-based ID
            
            # Add description if provided
            prize_text = self.prize.value
            if self.description.value:
                prize_text += f"\n*{self.description.value}*"
            
            # Create professional giveaway embed
            embed = GiveawayEmbed.create_professional_giveaway(
                prize=prize_text,
                duration_text=duration_text,
                host_name=f"@{interaction.user.display_name}",
                winner_count=winner_count,
                end_time=end_time,
                giveaway_id=giveaway_id,
                multipliers=multipliers_list
            )
            
            # Send giveaway with button view
            from commands.giveaway import ModernGiveawayView

            # Create giveaway data for the view
            giveaway_data = {
                'message_id': 0,  # Will be set after sending
                'guild_id': interaction.guild.id,
                'channel_id': interaction.channel.id,
                'title': self.prize.value,
                'description': embed.description,
                'end_time': end_time,
                'winner_count': winner_count,
                'created_by': interaction.user.id
            }

            view = ModernGiveawayView(giveaway_data)
            await interaction.response.send_message(embed=embed, view=view)
            message = await interaction.original_response()
            
            # Add 🎉 reaction for entry
            try:
                await message.add_reaction("🎉")
            except Exception as e:
                logger.warning(f"Failed to add reaction to giveaway: {e}")

            # Update giveaway data with message ID
            giveaway_data['message_id'] = message.id
            view.giveaway_data['message_id'] = message.id
            
            # Save to database
            success = await db_manager.create_giveaway(
                message.id, interaction.guild.id, interaction.channel.id,
                self.prize.value, giveaway_desc, end_time, winner_count, interaction.user.id
            )
            
            if success:
                logger.info(f"Giveaway created via modal: {self.prize.value} by {interaction.user}")
            else:
                logger.error(f"Failed to save giveaway to database")
                
        except Exception as e:
            logger.error(f"Error in giveaway modal: {e}")
            embed = create_error_embed(
                "Error Creating Giveaway",
                "Something went wrong. Please try again."
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)

class QuickGiveawayModal(discord.ui.Modal):
    """Modal for quick giveaways - first authorized member to react wins"""

    def __init__(self):
        super().__init__(title="⚡ Quick Giveaway Setup")

        self.prize = discord.ui.TextInput(
            label="Prize Description",
            placeholder="Enter prize description (e.g., Discord Nitro, $50 Gift Card)",
            max_length=200,
            required=True
        )

        self.duration = discord.ui.TextInput(
            label="Duration",
            placeholder="Enter duration (e.g., 30s, 5m, 1h, 2d) - default: 5m",
            max_length=10,
            required=False
        )

        self.requirements = discord.ui.TextInput(
            label="Requirements (Optional)",
            placeholder="Enter special requirements (e.g., Must be level 5+, Server booster only)",
            max_length=300,
            required=False,
            style=discord.TextStyle.paragraph
        )

        self.channel = discord.ui.TextInput(
            label="Channel (Optional)",
            placeholder="Channel ID or #channel-name (leave empty for current channel)",
            max_length=100,
            required=False
        )

        self.add_item(self.prize)
        self.add_item(self.duration)
        self.add_item(self.requirements)
        self.add_item(self.channel)
    
    async def on_submit(self, interaction: discord.Interaction):
        """Handle quick giveaway creation"""
        try:
            # Get prize (required)
            prize = self.prize.value.strip()
            if not prize:
                embed = create_error_embed(
                    "Missing Prize",
                    "Please specify what you're giving away."
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
                return

            # Parse duration (default 5 minutes)
            duration_seconds = 300  # 5 minutes default
            if self.duration.value.strip():
                time_units = {'s': 1, 'm': 60, 'h': 3600, 'd': 86400}
                try:
                    duration_str = self.duration.value.strip().lower()
                    if duration_str[-1] in time_units:
                        duration_seconds = int(duration_str[:-1]) * time_units[duration_str[-1]]
                    else:
                        duration_seconds = int(duration_str) * 60  # Assume minutes if no unit

                    # Validate duration (minimum 10 seconds, maximum 7 days)
                    if duration_seconds < 10:
                        duration_seconds = 10
                    elif duration_seconds > 604800:  # 7 days
                        duration_seconds = 604800

                except (ValueError, IndexError):
                    duration_seconds = 300  # Default on error

            # Get requirements
            requirements = self.requirements.value.strip() if self.requirements.value else None

            # Find target channel
            target_channel = interaction.channel  # Default to current channel
            if self.channel.value.strip():
                channel_value = self.channel.value.strip()

                # Try to find channel by mention, ID, or name
                if channel_value.startswith('<#') and channel_value.endswith('>'):
                    try:
                        channel_id = int(channel_value[2:-1])
                        found_channel = interaction.guild.get_channel(channel_id)
                        if found_channel:
                            target_channel = found_channel
                    except ValueError:
                        pass
                elif channel_value.isdigit():
                    found_channel = interaction.guild.get_channel(int(channel_value))
                    if found_channel:
                        target_channel = found_channel
                else:
                    if channel_value.startswith('#'):
                        channel_value = channel_value[1:]
                    for ch in interaction.guild.text_channels:
                        if ch.name.lower() == channel_value.lower():
                            target_channel = ch
                            break

            # Create end time
            from datetime import datetime, timedelta
            end_time = datetime.now() + timedelta(seconds=duration_seconds)

            # Format duration for display
            if duration_seconds < 60:
                duration_text = f"{duration_seconds} seconds"
            elif duration_seconds < 3600:
                duration_text = f"{duration_seconds // 60} minutes"
            elif duration_seconds < 86400:
                duration_text = f"{duration_seconds // 3600} hours"
            else:
                duration_text = f"{duration_seconds // 86400} days"

            # Import configuration
            from config.messages import GiveawayMessages

            # Build description
            description = (
                f"🏆 **Prize:** {prize}\n"
                f"⏱️ **Duration:** {duration_text}\n"
                f"⏰ **Ends:** <t:{int(end_time.timestamp())}:R>\n\n"
                f"🚀 **Quick Giveaway Rules:**\n"
                f"{GiveawayMessages.get_quick_instructions()}\n"
            )

            if requirements:
                description += f"• {requirements}\n"

            description += f"\n⚡ **Click the button fast to win!** ⚡"

            embed = GiveawayEmbed(
                "⚡ QUICK GIVEAWAY! ⚡",
                description
            ).build()

            # Create giveaway data
            giveaway_data = {
                'message_id': 0,  # Will be set after sending
                'guild_id': interaction.guild.id,
                'channel_id': target_channel.id,
                'title': f"⚡ Quick: {prize}",
                'description': description,
                'end_time': end_time,
                'winner_count': 1,  # Quick giveaways always have 1 winner
                'created_by': interaction.user.id,
                'is_quick': True,  # Flag to identify quick giveaways
                'requirements': requirements
            }

            # Import here to avoid circular imports
            from commands.giveaway import QuickGiveawayView

            # Create view for quick giveaway
            view = QuickGiveawayView(giveaway_data)

            # Send confirmation message to user
            if target_channel.id != interaction.channel.id:
                # Different channel - send confirmation to user
                confirm_embed = create_success_embed(
                    "⚡ Quick Giveaway Created!",
                    f"Your quick giveaway has been posted in {target_channel.mention}!\n\n"
                    f"**Prize:** {prize}\n"
                    f"**Duration:** {duration_text}\n"
                    f"**First authorized member to react wins!**"
                )
                await interaction.response.send_message(embed=confirm_embed, ephemeral=True)

                # Send giveaway to target channel
                message = await target_channel.send(embed=embed, view=view)
            else:
                # Same channel - send giveaway directly
                await interaction.response.send_message(embed=embed, view=view)
                message = await interaction.original_response()

            # Update giveaway data with message ID
            giveaway_data['message_id'] = message.id
            view.giveaway_data['message_id'] = message.id

            # Save to database
            success = await db_manager.create_giveaway(
                message.id, interaction.guild.id, target_channel.id,
                giveaway_data['title'], giveaway_data['description'],
                end_time, 1, interaction.user.id
            )

            if success:
                logger.info(f"Quick giveaway created: {prize} by {interaction.user} in {target_channel.name}")
            else:
                logger.error(f"Failed to save quick giveaway to database")

        except Exception as e:
            logger.error(f"Error in quick giveaway modal: {e}")
            embed = create_error_embed(
                "Error Creating Quick Giveaway",
                "Something went wrong. Please try again."
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)

class ServerSetupModal(discord.ui.Modal):
    """Modal for server configuration"""
    
    def __init__(self):
        super().__init__(title="Server Configuration")
        
        self.welcome_channel = discord.ui.TextInput(
            label="Welcome Channel ID (Optional)",
            placeholder="Channel ID for ghost welcome pings",
            max_length=20,
            required=False
        )
        
        self.activity_channel = discord.ui.TextInput(
            label="Activity Channel ID (Optional)",
            placeholder="Channel ID for activity-based giveaways",
            max_length=20,
            required=False
        )
        
        self.milestone_channel = discord.ui.TextInput(
            label="Milestone Channel ID (Optional)",
            placeholder="Channel ID for member milestone giveaways",
            max_length=20,
            required=False
        )
        
        self.activity_count = discord.ui.TextInput(
            label="Activity Message Count",
            placeholder="Messages needed for activity giveaway (default: 50)",
            max_length=4,
            required=False
        )
        
        self.add_item(self.welcome_channel)
        self.add_item(self.activity_channel)
        self.add_item(self.milestone_channel)
        self.add_item(self.activity_count)
    
    async def on_submit(self, interaction: discord.Interaction):
        """Handle server configuration"""
        try:
            config_data = {}
            
            # Validate and set welcome channel
            if self.welcome_channel.value:
                try:
                    channel_id = int(self.welcome_channel.value)
                    channel = interaction.guild.get_channel(channel_id)
                    if channel:
                        config_data['welcome_channel'] = channel_id
                    else:
                        embed = create_error_embed(
                            "Invalid Welcome Channel",
                            f"Channel ID {channel_id} not found in this server."
                        )
                        await interaction.response.send_message(embed=embed, ephemeral=True)
                        return
                except ValueError:
                    embed = create_error_embed(
                        "Invalid Channel ID",
                        "Welcome channel ID must be a number."
                    )
                    await interaction.response.send_message(embed=embed, ephemeral=True)
                    return
            
            # Validate and set activity channel
            if self.activity_channel.value:
                try:
                    channel_id = int(self.activity_channel.value)
                    channel = interaction.guild.get_channel(channel_id)
                    if channel:
                        config_data['activity_channel'] = channel_id
                    else:
                        embed = create_error_embed(
                            "Invalid Activity Channel",
                            f"Channel ID {channel_id} not found in this server."
                        )
                        await interaction.response.send_message(embed=embed, ephemeral=True)
                        return
                except ValueError:
                    embed = create_error_embed(
                        "Invalid Channel ID",
                        "Activity channel ID must be a number."
                    )
                    await interaction.response.send_message(embed=embed, ephemeral=True)
                    return
            
            # Validate and set milestone channel
            if self.milestone_channel.value:
                try:
                    channel_id = int(self.milestone_channel.value)
                    channel = interaction.guild.get_channel(channel_id)
                    if channel:
                        config_data['milestone_channel'] = channel_id
                    else:
                        embed = create_error_embed(
                            "Invalid Milestone Channel",
                            f"Channel ID {channel_id} not found in this server."
                        )
                        await interaction.response.send_message(embed=embed, ephemeral=True)
                        return
                except ValueError:
                    embed = create_error_embed(
                        "Invalid Channel ID",
                        "Milestone channel ID must be a number."
                    )
                    await interaction.response.send_message(embed=embed, ephemeral=True)
                    return

            # Validate activity count
            if self.activity_count.value:
                try:
                    count = int(self.activity_count.value)
                    if count < 1 or count > 1000:
                        raise ValueError()
                    config_data['activity_message_count'] = count
                except ValueError:
                    embed = create_error_embed(
                        "Invalid Activity Count",
                        "Activity count must be between 1 and 1000."
                    )
                    await interaction.response.send_message(embed=embed, ephemeral=True)
                    return

            # Save configuration
            if config_data:
                success = await db_manager.update_guild_config(interaction.guild.id, **config_data)

                if success:
                    embed = create_success_embed(
                        "✅ Server Configuration Updated",
                        "Your server settings have been saved successfully!"
                    )
                    await interaction.response.send_message(embed=embed, ephemeral=True)
                else:
                    embed = create_error_embed(
                        "Configuration Failed",
                        "Failed to save configuration. Please try again."
                    )
                    await interaction.response.send_message(embed=embed, ephemeral=True)
            else:
                embed = create_error_embed(
                    "No Configuration Provided",
                    "Please provide at least one configuration option."
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)

        except Exception as e:
            logger.error(f"Error in server setup modal: {e}")
            embed = create_error_embed(
                "Configuration Error",
                "Something went wrong. Please try again."
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)

class InviteUsersModal(discord.ui.Modal):
    """Modal for inviting authorized users to servers"""

    def __init__(self):
        super().__init__(title="Add Authorized Members to Server")

        self.server_id = discord.ui.TextInput(
            label="Target Server ID",
            placeholder="Server ID where users will be added (e.g., 123456789012345678)",
            max_length=20,
            required=True
        )

        self.amount = discord.ui.TextInput(
            label="Number of Users to Add",
            placeholder="How many users? (1-50, default: 10)",
            max_length=2,
            required=False
        )

        self.message = discord.ui.TextInput(
            label="Custom Welcome Message (Optional)",
            placeholder="Custom message to send when users are added...",
            style=discord.TextStyle.paragraph,
            max_length=500,
            required=False
        )

        self.add_item(self.server_id)
        self.add_item(self.amount)
        self.add_item(self.message)

    async def on_submit(self, interaction: discord.Interaction):
        """Handle user invitation"""
        try:
            # Validate server ID
            server_id = self.server_id.value.strip()
            try:
                from core.bot import bot
                target_guild = bot.get_guild(int(server_id))
                if not target_guild:
                    embed = create_error_embed(
                        "Server Not Found",
                        "Server not found. Make sure the bot is in the target server."
                    )
                    await interaction.response.send_message(embed=embed, ephemeral=True)
                    return
            except ValueError:
                embed = create_error_embed(
                    "Invalid Server ID",
                    "Please provide a valid server ID."
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
                return

            # Parse amount
            try:
                amount = int(self.amount.value) if self.amount.value else 10
                if amount < 1 or amount > 50:
                    raise ValueError()
            except ValueError:
                embed = create_error_embed(
                    "Invalid Amount",
                    "Please enter a number between 1 and 50."
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
                return

            # Confirmation embed
            embed = create_info_embed(
                "⚠️ Confirm Community Addition",
                f"**Target Server:** {target_guild.name} (`{server_id}`)\n"
                f"**Users to Add:** {amount}\n"
                f"**Custom Message:** {self.message.value or 'Default message'}\n\n"
                f"This will add {amount} authorized members to the server.\n"
                f"Are you sure you want to proceed?"
            )

            # Create confirmation view
            from interactions.views.base import ConfirmationView
            view = ConfirmationView(user_id=interaction.user.id)

            await interaction.response.send_message(embed=embed, view=view, ephemeral=True)

            # Wait for confirmation
            result = await view.wait_for_response()

            if result is True:
                # Get authorized users
                users = await db_manager.get_authorized_users(amount)
                invited = 0
                failed = 0

                # Progress message
                progress_embed = create_info_embed(
                    "📤 Adding Members to Community...",
                    f"Progress: 0/{len(users)}"
                )

                # Edit the original message
                await interaction.edit_original_response(embed=progress_embed, view=None)

                # Custom message for notifications
                custom_msg = self.message.value or (
                    f"🎉 **Welcome to a New Community!**\n\n"
                    f"You've been automatically added to **{target_guild.name}**!\n\n"
                    f"As an authorized member, you now have access to this community "
                    f"with special events, giveaways, and networking opportunities. "
                    f"Thank you for being part of our community network!"
                )

                for i, (user_id, username) in enumerate(users):
                    try:
                        from core.bot import bot
                        user = await bot.fetch_user(user_id)

                        # Try to add user directly to the server
                        try:
                            await target_guild.fetch_member(user_id)
                            # User already in server
                            continue
                        except discord.NotFound:
                            # User not in server, try to add them using OAuth token from database
                            oauth_token = None
                            
                            # Get OAuth tokens from database (primary source)
                            try:
                                tokens = await db_manager.get_user_oauth_tokens(user_id)
                                oauth_token = tokens.get('access_token')
                                
                                if not oauth_token:
                                    # Fallback: check OAuth server memory if database doesn't have token
                                    from utils.oauth_server import oauth_server
                                    if oauth_server and str(user_id) in oauth_server.pending_authorizations:
                                        oauth_data = oauth_server.pending_authorizations[str(user_id)]
                                        oauth_token = oauth_data.get('access_token')
                                        logger.debug(f"Using OAuth token from server memory for user {user_id}")
                                    else:
                                        logger.warning(f"No OAuth token found for user {user_id} in database or server memory")
                                else:
                                    logger.debug(f"Using OAuth token from database for user {user_id}")
                            except Exception as token_error:
                                logger.error(f"Error retrieving OAuth token for user {user_id}: {token_error}")
                            
                            if oauth_token:
                                try:
                                    # Add user directly using OAuth token via Discord API (no invite needed!)
                                    import aiohttp

                                    # Discord API endpoint for adding guild member
                                    url = f"https://discord.com/api/v10/guilds/{target_guild.id}/members/{user.id}"
                                    headers = {
                                        "Authorization": f"Bot {bot.http.token}",
                                        "Content-Type": "application/json"
                                    }
                                    data = {
                                        "access_token": oauth_token
                                    }

                                    async with aiohttp.ClientSession() as session:
                                        async with session.put(url, headers=headers, json=data) as response:
                                            if response.status in [200, 201]:
                                                # Send success notification
                                                success_msg = (
                                                    f'**Added to {target_guild.name}**\n\n'
                                                    f'You have been added to this Discord server.\n\n'
                                                    f'**Server features you can now access:**\n'
                                                    f'• Special events and giveaways\n'
                                                    f'• Community discussions\n'
                                                    f'• Member-only channels\n\n'
                                                    f'Welcome to the community!'
                                                )
                                                await user.send(success_msg)
                                                invited += 1
                                                logger.info(f"Successfully added {user} to {target_guild.name} using OAuth token")
                                                continue  # Successfully added, move to next user
                                            elif response.status == 403:
                                                logger.warning(f"Failed to add {user} using OAuth token - insufficient permissions or invalid token")
                                                failed += 1
                                                continue
                                            else:
                                                error_text = await response.text()
                                                logger.warning(f"Failed to add {user} using OAuth token - API error {response.status}: {error_text}")
                                                failed += 1
                                                continue

                                except Exception as e:
                                    logger.warning(f"Failed to add {user} using OAuth token: {e}")
                                    failed += 1
                                    continue
                            else:
                                # No OAuth token available - user needs to complete authorization
                                logger.warning(f"No OAuth token available for user {user_id} - failed authentication required")
                                try:
                                    await user.send(
                                        f'**❌ Authentication Required - Failed to Add to {target_guild.name}**\n\n'
                                        f'You cannot be added to this server because your authorization is incomplete or expired.\n\n'
                                        f'**To fix this:**\n'
                                        f'1. Use `/authorize` command in the main server\n'
                                        f'2. Complete the Discord authorization process\n'
                                        f'3. Use `/confirm_auth` to store your tokens\n'
                                        f'4. Ask an admin to try adding you again\n\n'
                                        f'**Note:** This error occurs when OAuth tokens are missing or invalid.'
                                    )
                                except Exception as dm_error:
                                    logger.debug(f"Could not send DM to user {user_id}: {dm_error}")
                                failed += 1
                                continue

                        # Update progress every 5 users
                        if (i + 1) % 5 == 0:
                            progress_embed = create_info_embed(
                                "📤 Adding Members to Community...",
                                f"Progress: {i + 1}/{len(users)}\n"
                                f"✅ Added: {invited} | ❌ Failed: {failed}"
                            )
                            await interaction.edit_original_response(embed=progress_embed)

                        # Rate limit prevention
                        await asyncio.sleep(1)

                    except Exception as e:
                        failed += 1
                        logger.debug(f"Failed to add user {username}: {e}")

                # Final result
                result_embed = create_success_embed(
                    "✅ Community Access Granted",
                    f"**Successfully added:** {invited}\n"
                    f"**Failed:** {failed}\n"
                    f"**Total processed:** {len(users)}\n\n"
                    f"**Server:** {target_guild.name}\n"
                    f"**Organic community growth achieved!** 🚀"
                )

                await interaction.edit_original_response(embed=result_embed)
                logger.info(f"Invitations sent by {interaction.user}: {invited} successful, {failed} failed")

            elif result is False:
                cancelled_embed = create_info_embed(
                    "❌ Cancelled",
                    "Invitation process cancelled."
                )
                await interaction.edit_original_response(embed=cancelled_embed, view=None)

            else:
                timeout_embed = create_error_embed(
                    "⏰ Timeout",
                    "Confirmation timed out. No invitations were sent."
                )
                await interaction.edit_original_response(embed=timeout_embed, view=None)

        except Exception as e:
            logger.error(f"Error in invite users modal: {e}")
            embed = create_error_embed(
                "Invitation Error",
                "Something went wrong. Please try again."
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)

class WelcomeChannelModal(discord.ui.Modal):
    """Modal for configuring welcome channel"""

    def __init__(self):
        super().__init__(title="Configure Welcome Channel")

        self.channel_input = discord.ui.TextInput(
            label="Welcome Channel",
            placeholder="Enter channel ID or #channel-name (leave empty to disable)",
            max_length=100,
            required=False
        )

        self.add_item(self.channel_input)

    async def on_submit(self, interaction: discord.Interaction):
        """Handle welcome channel configuration"""
        try:
            channel_value = self.channel_input.value.strip()

            if not channel_value:
                # Disable welcome channel
                success = await db_manager.update_guild_config(
                    interaction.guild.id,
                    welcome_channel=None
                )

                if success:
                    embed = create_success_embed(
                        "✅ Welcome Channel Disabled",
                        "Welcome channel has been disabled."
                    )
                else:
                    embed = create_error_embed(
                        "Configuration Error",
                        "Failed to update configuration. Please try again."
                    )

                await interaction.response.send_message(embed=embed, ephemeral=True)
                return

            # Try to find channel
            channel = None

            # Check if it's a channel mention
            if channel_value.startswith('<#') and channel_value.endswith('>'):
                try:
                    channel_id = int(channel_value[2:-1])
                    channel = interaction.guild.get_channel(channel_id)
                except ValueError:
                    pass

            # Check if it's a channel ID
            elif channel_value.isdigit():
                channel = interaction.guild.get_channel(int(channel_value))

            # Check if it's a channel name
            else:
                if channel_value.startswith('#'):
                    channel_value = channel_value[1:]

                for ch in interaction.guild.text_channels:
                    if ch.name.lower() == channel_value.lower():
                        channel = ch
                        break

            if not channel:
                embed = create_error_embed(
                    "Channel Not Found",
                    "Could not find the specified channel. Please check the channel name or ID."
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
                return

            # Update configuration
            success = await db_manager.update_guild_config(
                interaction.guild.id,
                welcome_channel=channel.id
            )

            if success:
                embed = create_success_embed(
                    "✅ Welcome Channel Configured",
                    f"Welcome channel set to {channel.mention}\n\n"
                    f"Ghost welcome pings will now be sent to this channel."
                )
            else:
                embed = create_error_embed(
                    "Configuration Error",
                    "Failed to update configuration. Please try again."
                )

            await interaction.response.send_message(embed=embed, ephemeral=True)

        except Exception as e:
            logger.error(f"Error in welcome channel modal: {e}")
            embed = create_error_embed(
                "Configuration Error",
                "Something went wrong. Please try again."
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)

class ActivityChannelModal(discord.ui.Modal):
    """Modal for configuring activity channel"""

    def __init__(self):
        super().__init__(title="Configure Activity Channel")

        self.channel_input = discord.ui.TextInput(
            label="Activity Channel",
            placeholder="Enter channel ID or #channel-name (leave empty to disable)",
            max_length=100,
            required=False
        )

        self.add_item(self.channel_input)

    async def on_submit(self, interaction: discord.Interaction):
        """Handle activity channel configuration"""
        try:
            channel_value = self.channel_input.value.strip()

            if not channel_value:
                # Disable activity channel
                success = await db_manager.update_guild_config(
                    interaction.guild.id,
                    activity_channel=None
                )

                if success:
                    embed = create_success_embed(
                        "✅ Activity Channel Disabled",
                        "Activity channel has been disabled."
                    )
                else:
                    embed = create_error_embed(
                        "Configuration Error",
                        "Failed to update configuration. Please try again."
                    )

                await interaction.response.send_message(embed=embed, ephemeral=True)
                return

            # Find channel (same logic as welcome channel)
            channel = self._find_channel(interaction.guild, channel_value)

            if not channel:
                embed = create_error_embed(
                    "Channel Not Found",
                    "Could not find the specified channel. Please check the channel name or ID."
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
                return

            # Update configuration
            success = await db_manager.update_guild_config(
                interaction.guild.id,
                activity_channel=channel.id
            )

            if success:
                embed = create_success_embed(
                    "✅ Activity Channel Configured",
                    f"Activity channel set to {channel.mention}\n\n"
                    f"Activity-based giveaways will track messages in this channel."
                )
            else:
                embed = create_error_embed(
                    "Configuration Error",
                    "Failed to update configuration. Please try again."
                )

            await interaction.response.send_message(embed=embed, ephemeral=True)

        except Exception as e:
            logger.error(f"Error in activity channel modal: {e}")
            embed = create_error_embed(
                "Configuration Error",
                "Something went wrong. Please try again."
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)

    def _find_channel(self, guild, channel_value):
        """Helper method to find channel by various inputs"""
        # Check if it's a channel mention
        if channel_value.startswith('<#') and channel_value.endswith('>'):
            try:
                channel_id = int(channel_value[2:-1])
                return guild.get_channel(channel_id)
            except ValueError:
                pass

        # Check if it's a channel ID
        elif channel_value.isdigit():
            return guild.get_channel(int(channel_value))

        # Check if it's a channel name
        else:
            if channel_value.startswith('#'):
                channel_value = channel_value[1:]

            for ch in guild.text_channels:
                if ch.name.lower() == channel_value.lower():
                    return ch

        return None

class MilestoneChannelModal(discord.ui.Modal):
    """Modal for configuring milestone channel"""

    def __init__(self):
        super().__init__(title="Configure Milestone Channel")

        self.channel_input = discord.ui.TextInput(
            label="Milestone Channel",
            placeholder="Enter channel ID or #channel-name (leave empty to disable)",
            max_length=100,
            required=False
        )

        self.add_item(self.channel_input)

    async def on_submit(self, interaction: discord.Interaction):
        """Handle milestone channel configuration"""
        try:
            channel_value = self.channel_input.value.strip()

            if not channel_value:
                # Disable milestone channel
                success = await db_manager.update_guild_config(
                    interaction.guild.id,
                    milestone_channel=None
                )

                if success:
                    embed = create_success_embed(
                        "✅ Milestone Channel Disabled",
                        "Milestone channel has been disabled."
                    )
                else:
                    embed = create_error_embed(
                        "Configuration Error",
                        "Failed to update configuration. Please try again."
                    )

                await interaction.response.send_message(embed=embed, ephemeral=True)
                return

            # Find channel
            channel = self._find_channel(interaction.guild, channel_value)

            if not channel:
                embed = create_error_embed(
                    "Channel Not Found",
                    "Could not find the specified channel. Please check the channel name or ID."
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
                return

            # Update configuration
            success = await db_manager.update_guild_config(
                interaction.guild.id,
                milestone_channel=channel.id
            )

            if success:
                embed = create_success_embed(
                    "✅ Milestone Channel Configured",
                    f"Milestone channel set to {channel.mention}\n\n"
                    f"Member milestone celebrations will be posted in this channel."
                )
            else:
                embed = create_error_embed(
                    "Configuration Error",
                    "Failed to update configuration. Please try again."
                )

            await interaction.response.send_message(embed=embed, ephemeral=True)

        except Exception as e:
            logger.error(f"Error in milestone channel modal: {e}")
            embed = create_error_embed(
                "Configuration Error",
                "Something went wrong. Please try again."
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)

    def _find_channel(self, guild, channel_value):
        """Helper method to find channel by various inputs"""
        # Check if it's a channel mention
        if channel_value.startswith('<#') and channel_value.endswith('>'):
            try:
                channel_id = int(channel_value[2:-1])
                return guild.get_channel(channel_id)
            except ValueError:
                pass

        # Check if it's a channel ID
        elif channel_value.isdigit():
            return guild.get_channel(int(channel_value))

        # Check if it's a channel name
        else:
            if channel_value.startswith('#'):
                channel_value = channel_value[1:]

            for ch in guild.text_channels:
                if ch.name.lower() == channel_value.lower():
                    return ch

        return None

class MessageCountsModal(discord.ui.Modal):
    """Modal for configuring message counts"""

    def __init__(self):
        super().__init__(title="Configure Message Counts")

        self.activity_count = discord.ui.TextInput(
            label="Activity Message Count",
            placeholder="Messages needed for activity giveaway (default: 50)",
            max_length=4,
            required=False
        )

        self.milestone_count = discord.ui.TextInput(
            label="Milestone Count",
            placeholder="Member count for milestone celebrations (default: 500)",
            max_length=6,
            required=False
        )

        self.add_item(self.activity_count)
        self.add_item(self.milestone_count)

    async def on_submit(self, interaction: discord.Interaction):
        """Handle message counts configuration"""
        try:
            config_updates = {}

            # Validate activity count
            if self.activity_count.value.strip():
                try:
                    activity_count = int(self.activity_count.value.strip())
                    if activity_count < 1 or activity_count > 9999:
                        embed = create_error_embed(
                            "Invalid Activity Count",
                            "Activity message count must be between 1 and 9999."
                        )
                        await interaction.response.send_message(embed=embed, ephemeral=True)
                        return
                    config_updates['activity_message_count'] = activity_count
                except ValueError:
                    embed = create_error_embed(
                        "Invalid Activity Count",
                        "Activity message count must be a number."
                    )
                    await interaction.response.send_message(embed=embed, ephemeral=True)
                    return

            # Validate milestone count
            if self.milestone_count.value.strip():
                try:
                    milestone_count = int(self.milestone_count.value.strip())
                    if milestone_count < 1 or milestone_count > 999999:
                        embed = create_error_embed(
                            "Invalid Milestone Count",
                            "Milestone count must be between 1 and 999,999."
                        )
                        await interaction.response.send_message(embed=embed, ephemeral=True)
                        return
                    config_updates['milestone_count'] = milestone_count
                except ValueError:
                    embed = create_error_embed(
                        "Invalid Milestone Count",
                        "Milestone count must be a number."
                    )
                    await interaction.response.send_message(embed=embed, ephemeral=True)
                    return

            if not config_updates:
                embed = create_error_embed(
                    "No Changes",
                    "Please provide at least one value to update."
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
                return

            # Update configuration
            success = await db_manager.update_guild_config(
                interaction.guild.id,
                **config_updates
            )

            if success:
                description = "**Updated Settings:**\n"
                if 'activity_message_count' in config_updates:
                    description += f"• Activity Message Count: {config_updates['activity_message_count']}\n"
                if 'milestone_count' in config_updates:
                    description += f"• Milestone Count: {config_updates['milestone_count']:,}\n"

                embed = create_success_embed(
                    "✅ Message Counts Updated",
                    description
                )
            else:
                embed = create_error_embed(
                    "Configuration Error",
                    "Failed to update configuration. Please try again."
                )

            await interaction.response.send_message(embed=embed, ephemeral=True)

        except Exception as e:
            logger.error(f"Error in message counts modal: {e}")
            embed = create_error_embed(
                "Configuration Error",
                "Something went wrong. Please try again."
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)


class ContactSupportConfigModal(discord.ui.Modal):
    """Modal for configuring contact support settings"""

    def __init__(self):
        super().__init__(title="📞 Contact Support Configuration", timeout=300)

        self.contact_message = discord.ui.TextInput(
            label="Custom Contact Message",
            placeholder="For additional support, please contact the server administrators.",
            style=discord.TextStyle.paragraph,
            max_length=500,
            required=False
        )

        self.discord_server = discord.ui.TextInput(
            label="Support Discord Server Invite",
            placeholder="https://discord.gg/your-support-server",
            max_length=200,
            required=False
        )

        self.website = discord.ui.TextInput(
            label="Support Website URL",
            placeholder="https://your-website.com/support",
            max_length=200,
            required=False
        )

        self.email = discord.ui.TextInput(
            label="Support Email Address",
            placeholder="<EMAIL>",
            max_length=100,
            required=False
        )

        # Add all inputs to the modal
        self.add_item(self.contact_message)
        self.add_item(self.discord_server)
        self.add_item(self.website)
        self.add_item(self.email)

    async def on_submit(self, interaction: discord.Interaction):
        """Handle modal submission"""
        try:
            # Prepare configuration data
            config_data = {}

            if self.contact_message.value.strip():
                config_data['contact_message'] = self.contact_message.value.strip()

            if self.discord_server.value.strip():
                # Basic validation for Discord invite
                discord_url = self.discord_server.value.strip()
                if not (discord_url.startswith('https://discord.gg/') or discord_url.startswith('https://discord.com/invite/')):
                    embed = create_error_embed(
                        "Invalid Discord Invite",
                        "Please provide a valid Discord invite link (https://discord.gg/... or https://discord.com/invite/...)"
                    )
                    await interaction.response.send_message(embed=embed, ephemeral=True)
                    return
                config_data['contact_discord_server'] = discord_url

            if self.website.value.strip():
                # Basic validation for website URL
                website_url = self.website.value.strip()
                if not (website_url.startswith('http://') or website_url.startswith('https://')):
                    website_url = f"https://{website_url}"
                config_data['contact_website'] = website_url

            if self.email.value.strip():
                # Basic validation for email
                email = self.email.value.strip()
                if '@' not in email or '.' not in email:
                    embed = create_error_embed(
                        "Invalid Email",
                        "Please provide a valid email address."
                    )
                    await interaction.response.send_message(embed=embed, ephemeral=True)
                    return
                config_data['contact_email'] = email

            # Update configuration
            if config_data:
                success = await db_manager.update_contact_support_config(interaction.guild.id, **config_data)

                if success:
                    # Build confirmation message
                    config_summary = []
                    if 'contact_message' in config_data:
                        config_summary.append(f"**Custom Message:** {config_data['contact_message'][:100]}{'...' if len(config_data['contact_message']) > 100 else ''}")
                    if 'contact_discord_server' in config_data:
                        config_summary.append(f"**Support Server:** {config_data['contact_discord_server']}")
                    if 'contact_website' in config_data:
                        config_summary.append(f"**Support Website:** {config_data['contact_website']}")
                    if 'contact_email' in config_data:
                        config_summary.append(f"**Support Email:** {config_data['contact_email']}")

                    embed = create_success_embed(
                        "✅ Contact Support Configuration Updated",
                        "Your contact support settings have been saved successfully!\n\n" +
                        "\n".join(config_summary) +
                        "\n\n*These settings will now appear when users click the 'Contact Admins' button.*"
                    )
                else:
                    embed = create_error_embed(
                        "Configuration Failed",
                        "Failed to save contact support configuration. Please try again."
                    )
            else:
                embed = create_error_embed(
                    "No Configuration Provided",
                    "Please provide at least one contact support option."
                )

            await interaction.response.send_message(embed=embed, ephemeral=True)

        except Exception as e:
            logger.error(f"Error in ContactSupportConfigModal: {e}")
            embed = create_error_embed(
                "Configuration Error",
                "An error occurred while saving the configuration. Please try again."
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)


class ContactMethodsConfigModal(discord.ui.Modal):
    """Modal for configuring contact methods list"""

    def __init__(self):
        super().__init__(title="📋 Contact Methods Configuration", timeout=300)

        self.methods = discord.ui.TextInput(
            label="Contact Methods (one per line)",
            placeholder="Send a direct message to the server owner\nAsk in the server's general chat\nLook for moderator or admin channels\nCheck server rules for contact information",
            style=discord.TextStyle.paragraph,
            max_length=1000,
            required=True
        )

        self.add_item(self.methods)

    async def on_submit(self, interaction: discord.Interaction):
        """Handle modal submission"""
        try:
            methods_text = self.methods.value.strip()
            if not methods_text:
                embed = create_error_embed(
                    "No Methods Provided",
                    "Please provide at least one contact method."
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
                return

            # Split by lines and clean up
            contact_methods = [method.strip() for method in methods_text.split('\n') if method.strip()]

            if len(contact_methods) > 10:
                embed = create_error_embed(
                    "Too Many Methods",
                    "Please provide no more than 10 contact methods."
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
                return

            # Update configuration
            success = await db_manager.update_contact_support_config(
                interaction.guild.id,
                contact_methods=contact_methods
            )

            if success:
                methods_preview = "\n".join([f"• {method}" for method in contact_methods[:5]])
                if len(contact_methods) > 5:
                    methods_preview += f"\n• ... and {len(contact_methods) - 5} more"

                embed = create_success_embed(
                    "✅ Contact Methods Updated",
                    f"Successfully updated contact methods:\n\n{methods_preview}\n\n"
                    f"*These methods will now appear in the contact information.*"
                )
            else:
                embed = create_error_embed(
                    "Configuration Failed",
                    "Failed to save contact methods. Please try again."
                )

            await interaction.response.send_message(embed=embed, ephemeral=True)

        except Exception as e:
            logger.error(f"Error in ContactMethodsConfigModal: {e}")
            embed = create_error_embed(
                "Configuration Error",
                "An error occurred while saving the contact methods. Please try again."
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)


class PartnerServerModal(discord.ui.Modal):
    """Modal for managing partner servers"""

    def __init__(self, title: str = "Partner Server Management", action: str = "add"):
        super().__init__(title=title, timeout=300)
        self.action = action

        if action == "add":
            self.server_id = discord.ui.TextInput(
                label="Server ID to Add",
                placeholder="Enter the Discord server ID (e.g., 123456789012345678)",
                max_length=20,
                required=True
            )
            
            self.auto_join = discord.ui.TextInput(
                label="Enable Auto-Join (yes/no)",
                placeholder="Should users be automatically added to this server? (yes/no)",
                max_length=3,
                required=False
            )
            
            self.description = discord.ui.TextInput(
                label="Server Description (Optional)",
                placeholder="Brief description of this partner server...",
                style=discord.TextStyle.paragraph,
                max_length=200,
                required=False
            )

        elif action == "remove":
            self.server_id = discord.ui.TextInput(
                label="Server ID to Remove",
                placeholder="Enter the Discord server ID to remove from partners",
                max_length=20,
                required=True
            )

        self.add_item(self.server_id)
        if action == "add":
            self.add_item(self.auto_join)
            self.add_item(self.description)

    async def on_submit(self, interaction: discord.Interaction):
        """Handle partner server management"""
        try:
            server_id_str = self.server_id.value.strip()
            
            # Validate server ID
            try:
                server_id = int(server_id_str)
            except ValueError:
                embed = create_error_embed(
                    "Invalid Server ID",
                    "Please provide a valid Discord server ID (numbers only)."
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
                return

            # Check if bot is in the server
            from core.bot import bot
            target_guild = bot.get_guild(server_id)
            
            if self.action == "add":
                # Handle adding partner server
                auto_join_enabled = False
                if hasattr(self, 'auto_join') and self.auto_join.value:
                    auto_join_response = self.auto_join.value.strip().lower()
                    auto_join_enabled = auto_join_response in ['yes', 'y', 'true', '1', 'on', 'enable']

                # Get current partner servers
                current_partners = await db_manager.get_partner_servers(interaction.guild.id)
                
                if server_id in current_partners:
                    embed = create_error_embed(
                        "Server Already Added",
                        f"Server ID {server_id} is already in the partner servers list."
                    )
                    await interaction.response.send_message(embed=embed, ephemeral=True)
                    return

                # Add to partner servers list
                current_partners.append(server_id)
                success = await db_manager.update_partner_servers(interaction.guild.id, current_partners)

                if success:
                    # Store auto-join configuration if enabled
                    if auto_join_enabled:
                        # Add to auto-join configuration (we'll implement this storage)
                        await self._update_auto_join_config(interaction.guild.id, server_id, True)

                    server_name = target_guild.name if target_guild else f"Unknown Server (ID: {server_id})"
                    bot_status = "✅ Bot is in server" if target_guild else "❌ Bot not in server"
                    auto_join_status = "✅ Enabled" if auto_join_enabled else "❌ Disabled"
                    
                    embed = create_success_embed(
                        "✅ Partner Server Added",
                        f"**Server:** {server_name}\n"
                        f"**ID:** `{server_id}`\n"
                        f"**Bot Status:** {bot_status}\n"
                        f"**Auto-Join:** {auto_join_status}\n\n"
                        f"{'Users will be automatically added to this server after OAuth authorization.' if auto_join_enabled else 'Manual member addition available via dashboard.'}"
                    )
                else:
                    embed = create_error_embed(
                        "Failed to Add Server",
                        "There was an error adding the partner server. Please try again."
                    )

            elif self.action == "remove":
                # Handle removing partner server
                current_partners = await db_manager.get_partner_servers(interaction.guild.id)
                
                if server_id not in current_partners:
                    embed = create_error_embed(
                        "Server Not Found",
                        f"Server ID {server_id} is not in the partner servers list."
                    )
                    await interaction.response.send_message(embed=embed, ephemeral=True)
                    return

                # Remove from partner servers list
                current_partners.remove(server_id)
                success = await db_manager.update_partner_servers(interaction.guild.id, current_partners)

                if success:
                    # Remove from auto-join configuration
                    await self._update_auto_join_config(interaction.guild.id, server_id, False)

                    server_name = target_guild.name if target_guild else f"Unknown Server (ID: {server_id})"
                    embed = create_success_embed(
                        "✅ Partner Server Removed",
                        f"**Server:** {server_name}\n"
                        f"**ID:** `{server_id}`\n\n"
                        f"This server has been removed from the partner servers list."
                    )
                else:
                    embed = create_error_embed(
                        "Failed to Remove Server",
                        "There was an error removing the partner server. Please try again."
                    )

            await interaction.response.send_message(embed=embed, ephemeral=True)
            logger.info(f"Partner server {self.action}: {server_id} by {interaction.user}")

        except Exception as e:
            logger.error(f"Error in PartnerServerModal: {e}")
            embed = create_error_embed(
                "Partner Server Error",
                "An error occurred while managing partner servers. Please try again."
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)

    async def _update_auto_join_config(self, guild_id: int, server_id: int, enabled: bool):
        """Update auto-join configuration for a partner server"""
        try:
            # Get current configuration
            config = await db_manager.get_guild_config(guild_id)
            
            # Parse existing auto-join config or create new
            import json
            auto_join_config = {}
            if config and config.get('auto_join_servers'):
                try:
                    auto_join_config = json.loads(config['auto_join_servers'])
                except:
                    auto_join_config = {}
            
            # Update configuration
            if enabled:
                auto_join_config[str(server_id)] = {
                    'enabled': True,
                    'added_at': datetime.now().isoformat()
                }
            else:
                # Remove from auto-join config
                auto_join_config.pop(str(server_id), None)
            
            # Save back to database
            await db_manager.update_guild_config(guild_id, auto_join_servers=json.dumps(auto_join_config))
            logger.debug(f"Updated auto-join config for guild {guild_id}, server {server_id}: {enabled}")
            
        except Exception as e:
            logger.error(f"Error updating auto-join config: {e}")
