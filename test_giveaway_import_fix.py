#!/usr/bin/env python3
"""
Test script to verify the GiveawayView import fix.
This script tests that the import errors are resolved and the correct classes are imported.
"""

import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_giveaway_imports():
    """Test that all giveaway-related imports work correctly"""
    print("🧪 Testing Giveaway Import Fixes")
    print("=" * 50)
    
    try:
        # Test 1: Import ModernGiveawayView from commands.giveaway
        print("\n📦 Testing commands.giveaway imports...")
        try:
            from commands.giveaway import ModernGiveawayView, QuickGiveawayView
            print("   ✅ ModernGiveawayView imported successfully")
            print("   ✅ QuickGiveawayView imported successfully")
            
            # Verify the classes exist and are callable
            assert callable(ModernGiveawayView), "ModernGiveawayView should be callable"
            assert callable(QuickGiveawayView), "QuickGiveawayView should be callable"
            print("   ✅ Classes are callable")
            
        except ImportError as e:
            print(f"   ❌ Import error in commands.giveaway: {e}")
            return False
        
        # Test 2: Test the fixed import in prize_selection.py
        print("\n🎁 Testing prize_selection.py imports...")
        try:
            # This should now work without errors
            import interactions.prize_selection
            print("   ✅ interactions.prize_selection imported successfully")
            
            # Check if the module has the expected classes
            assert hasattr(interactions.prize_selection, 'NitroPrizeModal'), "NitroPrizeModal should exist"
            assert hasattr(interactions.prize_selection, 'RobuxPrizeModal'), "RobuxPrizeModal should exist"
            print("   ✅ Prize modal classes found")
            
        except ImportError as e:
            print(f"   ❌ Import error in prize_selection: {e}")
            return False
        except Exception as e:
            print(f"   ❌ Error in prize_selection: {e}")
            return False
        
        # Test 3: Test the fixed import in modals.py
        print("\n📝 Testing modals.py imports...")
        try:
            # This should now work without errors
            import interactions.modals
            print("   ✅ interactions.modals imported successfully")
            
            # Check if the module has the expected classes
            assert hasattr(interactions.modals, 'GiveawayModal'), "GiveawayModal should exist"
            print("   ✅ GiveawayModal class found")
            
        except ImportError as e:
            print(f"   ❌ Import error in modals: {e}")
            return False
        except Exception as e:
            print(f"   ❌ Error in modals: {e}")
            return False
        
        # Test 4: Verify that the old GiveawayView import would fail
        print("\n🚫 Testing that old GiveawayView import fails (as expected)...")
        try:
            from commands.giveaway import GiveawayView
            print("   ⚠️  WARNING: GiveawayView import succeeded - this should not happen!")
            return False
        except ImportError:
            print("   ✅ GiveawayView import correctly fails (class doesn't exist)")
        
        print("\n" + "=" * 50)
        print("🎉 ALL IMPORT TESTS PASSED!")
        print("✅ The GiveawayView import error has been successfully fixed.")
        print("✅ Nitro giveaway creation should now work without import errors.")
        return True
        
    except Exception as e:
        print(f"\n❌ CRITICAL ERROR: {e}")
        return False

def test_class_functionality():
    """Test basic functionality of the imported classes"""
    print("\n🔧 Testing Class Functionality")
    print("=" * 50)
    
    try:
        from commands.giveaway import ModernGiveawayView
        
        # Test that we can create an instance with sample data
        sample_giveaway_data = {
            'message_id': 123456789,
            'guild_id': 987654321,
            'channel_id': 555666777,
            'title': 'Test Prize',
            'description': 'Test Description',
            'end_time': None,  # Would be a datetime in real usage
            'winner_count': 1,
            'created_by': 111222333
        }
        
        print("\n🏗️  Testing ModernGiveawayView instantiation...")
        try:
            # This should work without errors
            view = ModernGiveawayView(sample_giveaway_data)
            print("   ✅ ModernGiveawayView created successfully")
            
            # Check that it has the expected attributes
            assert hasattr(view, 'giveaway_data'), "View should have giveaway_data attribute"
            assert view.giveaway_data == sample_giveaway_data, "Giveaway data should be stored correctly"
            print("   ✅ Giveaway data stored correctly")
            
            # Check that it has UI components
            assert hasattr(view, 'children'), "View should have children (UI components)"
            print(f"   ✅ View has {len(view.children)} UI components")
            
        except Exception as e:
            print(f"   ❌ Error creating ModernGiveawayView: {e}")
            return False
        
        print("\n" + "=" * 50)
        print("🎉 CLASS FUNCTIONALITY TESTS PASSED!")
        print("✅ ModernGiveawayView works correctly with sample data.")
        return True
        
    except Exception as e:
        print(f"\n❌ FUNCTIONALITY TEST ERROR: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Starting GiveawayView Import Fix Verification")
    print("=" * 80)
    
    # Test 1: Import fixes
    import_test_passed = test_giveaway_imports()
    
    # Test 2: Basic functionality
    functionality_test_passed = test_class_functionality()
    
    print("\n" + "=" * 80)
    if import_test_passed and functionality_test_passed:
        print("🎉 ALL TESTS PASSED! The GiveawayView import fix is working correctly.")
        print("✅ Nitro giveaway creation should now work without import errors.")
        print("✅ Regular giveaway creation through modals should also work correctly.")
        return 0
    else:
        print("❌ SOME TESTS FAILED! Please review the errors above.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
