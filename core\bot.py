"""
Memory-optimized Discord bot client.
Implements discord.py v2.0+ features with minimal memory footprint.
"""
import discord
from discord.ext import commands, tasks
import asyncio
import gc
from typing import Optional, Dict, Any
from datetime import datetime, timedelta

from .config import config, intents_config, Colors
from .logging import logger
from .exceptions import BotException, MemoryLimitError

class MemoryOptimizedBot(commands.Bot):
    """
    Memory-optimized Discord bot with efficient resource management.
    
    Features:
    - Disabled message cache (max_messages=None)
    - Minimal intents configuration
    - Automatic garbage collection
    - Resource cleanup on shutdown
    """
    
    def __init__(self):
        # Configure minimal intents for memory efficiency
        intents = discord.Intents.none()
        intents.guilds = intents_config.guilds
        intents.guild_messages = intents_config.guild_messages
        intents.guild_reactions = intents_config.guild_reactions
        intents.message_content = intents_config.message_content
        intents.integrations = intents_config.guild_integrations
        intents.members = intents_config.members  # Required for member events and guild join events
        
        super().__init__(
            command_prefix=config.command_prefix,
            intents=intents,
            case_insensitive=config.case_insensitive,
            strip_after_prefix=config.strip_after_prefix,
            # CRITICAL: Disable message cache for memory optimization
            max_messages=config.memory.max_messages,
            # Reduce member cache
            member_cache_flags=discord.MemberCacheFlags.none(),
            # Disable chunk guilds on startup
            chunk_guilds_at_startup=False,
            # Reduce heartbeat timeout
            heartbeat_timeout=30.0
        )
        
        # Memory tracking
        self._start_time = datetime.now()
        self._view_registry: Dict[str, discord.ui.View] = {}
        self._cleanup_tasks = []
        
        # Database connection pool (will be initialized later)
        self.db_pool = None
        
        logger.info("Memory-optimized bot initialized")
    
    async def setup_hook(self):
        """Initialize bot components after login"""
        logger.info("Setting up bot components...")
        
        # Start memory cleanup task
        self.memory_cleanup.start()
        
        # Initialize database pool
        await self._init_database_pool()
        
        # Initialize OAuth2 server
        await self._init_oauth_server()
        
        # Initialize advanced token manager
        await self._init_token_manager()
        
        logger.info("Bot setup completed")
    
    async def _init_oauth_server(self):
        """Initialize OAuth2 callback server"""
        try:
            from utils.oauth_server import start_oauth_server
            
            # Start OAuth2 server
            success = await start_oauth_server()
            if success:
                logger.info("OAuth2 callback server started successfully")
            else:
                logger.warning("OAuth2 callback server failed to start - OAuth features may not work")
                
        except Exception as e:
            logger.error(f"Failed to initialize OAuth2 server: {e}")
            logger.warning("OAuth features may not work properly")
    
    async def _init_database_pool(self):
        """Initialize database connection pool"""
        try:
            import aiosqlite
            # For SQLite, we'll use a simple connection manager
            # In production, consider using a proper pool
            self.db_pool = aiosqlite
            logger.info("Database pool initialized")
        except ImportError:
            logger.error("aiosqlite not available, database operations will fail")

    async def _init_token_manager(self):
        """Initialize advanced OAuth token manager"""
        try:
            from utils.oauth_advanced import start_token_manager
            
            # Start advanced token monitoring
            await start_token_manager()
            logger.info("Advanced OAuth token manager started successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize token manager: {e}")
            logger.warning("Advanced token features may not work properly")

    async def _smart_sync_commands(self):
        """Smart command sync - only sync when commands actually change"""
        import hashlib
        import json
        import os

        # Generate hash of current commands
        commands_data = []
        for command in self.tree.get_commands():
            cmd_data = {
                'name': command.name,
                'description': command.description,
                'options': []
            }
            # Add parameters if any
            if hasattr(command, 'parameters'):
                for param in command.parameters:
                    cmd_data['options'].append({
                        'name': param.name,
                        'description': param.description,
                        'type': str(param.type) if hasattr(param, 'type') else 'unknown'
                    })
            commands_data.append(cmd_data)

        # Sort for consistent hashing
        commands_data.sort(key=lambda x: x['name'])
        commands_json = json.dumps(commands_data, sort_keys=True)
        current_hash = hashlib.md5(commands_json.encode()).hexdigest()

        # Check if hash file exists and compare
        hash_file = 'data/command_hash.txt'
        os.makedirs('data', exist_ok=True)

        try:
            if os.path.exists(hash_file):
                with open(hash_file, 'r') as f:
                    stored_hash = f.read().strip()

                if stored_hash == current_hash:
                    logger.info("✅ Commands unchanged, skipping sync")
                    return
        except Exception as e:
            logger.warning(f"Could not read command hash: {e}")

        # Commands changed or first run - sync them
        logger.info("Syncing slash commands...")
        try:
            # Add timeout to prevent hanging
            synced = await asyncio.wait_for(self.tree.sync(), timeout=30.0)
            logger.info(f"✅ Synced {len(synced)} slash commands successfully!")
        except asyncio.TimeoutError:
            logger.error("❌ Slash command sync timed out after 30 seconds")
            logger.warning("This may be due to Discord rate limiting or network issues")
            raise
        except Exception as e:
            logger.error(f"❌ Slash command sync failed: {e}")
            raise

        # Store new hash
        try:
            with open(hash_file, 'w') as f:
                f.write(current_hash)
        except Exception as e:
            logger.warning(f"Could not save command hash: {e}")

    async def on_ready(self):
        """Called when bot is ready"""
        logger.info(f"Bot logged in as {self.user}")
        logger.info(f"Connected to {len(self.guilds)} guilds")

        # List connected guilds
        if self.guilds:
            for guild in self.guilds:
                logger.info(f"  • {guild.name} (ID: {guild.id}, Members: {guild.member_count})")

        # Smart sync slash commands - only sync when commands change
        try:
            await self._smart_sync_commands()
        except Exception as e:
            logger.error(f"Failed to sync slash commands: {e}")

        # Set bot status
        try:
            activity = discord.Activity(
                type=discord.ActivityType.watching,
                name="for giveaways 🎉"
            )
            await self.change_presence(activity=activity, status=discord.Status.online)
            logger.info("Bot status set successfully")
        except Exception as e:
            logger.error(f"Failed to set bot status: {e}")
    
    async def on_error(self, event_method: str, *args, **kwargs):
        """Handle errors with memory-efficient logging"""
        logger.error(f"Error in {event_method}: {args}")
        
        # Force garbage collection on errors
        gc.collect()
    
    async def close(self):
        """Clean shutdown with resource cleanup"""
        logger.info("Shutting down bot...")
        
        # Stop OAuth2 server
        try:
            from utils.oauth_server import stop_oauth_server
            await stop_oauth_server()
            logger.info("OAuth2 server stopped")
        except Exception as e:
            logger.error(f"Error stopping OAuth2 server: {e}")
        
        # Stop advanced token manager
        try:
            from utils.oauth_advanced import stop_token_manager
            await stop_token_manager()
            logger.info("Advanced token manager stopped")
        except Exception as e:
            logger.error(f"Error stopping token manager: {e}")
        
        # Stop cleanup tasks
        if hasattr(self, 'memory_cleanup'):
            self.memory_cleanup.cancel()
        
        # Cancel all cleanup tasks
        for task in self._cleanup_tasks:
            if not task.done():
                task.cancel()
        
        # Clear view registry
        self._view_registry.clear()
        
        # Close database connections
        if self.db_pool:
            # For aiosqlite, connections are closed automatically
            pass
        
        # Force garbage collection
        gc.collect()
        
        await super().close()
        logger.info("Bot shutdown completed")
    
    @tasks.loop(seconds=300)  # Every 5 minutes
    async def memory_cleanup(self):
        """Periodic memory cleanup task"""
        try:
            # Force garbage collection
            collected = gc.collect()
            
            # Clean up expired views
            await self._cleanup_expired_views()
            
            # Log memory stats
            if collected > 0:
                logger.debug(f"Garbage collection: {collected} objects collected")
            
        except Exception as e:
            logger.error(f"Error in memory cleanup: {e}")
    
    async def _cleanup_expired_views(self):
        """Clean up expired views from registry"""
        current_time = datetime.now()
        expired_views = []
        
        for view_id, view in self._view_registry.items():
            # Check if view has timed out (assuming 1 hour default)
            if hasattr(view, '_created_at'):
                if current_time - view._created_at > timedelta(hours=1):
                    expired_views.append(view_id)
        
        # Remove expired views
        for view_id in expired_views:
            del self._view_registry[view_id]
            logger.debug(f"Cleaned up expired view: {view_id}")
    
    def register_view(self, view_id: str, view: discord.ui.View):
        """Register a view for cleanup tracking"""
        view._created_at = datetime.now()
        self._view_registry[view_id] = view
        
        # Limit registry size for memory efficiency
        if len(self._view_registry) > config.memory.max_cached_users:
            # Remove oldest view
            oldest_id = min(self._view_registry.keys(), 
                          key=lambda k: self._view_registry[k]._created_at)
            del self._view_registry[oldest_id]
            logger.debug(f"Registry full, removed oldest view: {oldest_id}")
    
    def unregister_view(self, view_id: str):
        """Unregister a view from cleanup tracking"""
        if view_id in self._view_registry:
            del self._view_registry[view_id]
            logger.debug(f"Unregistered view: {view_id}")
    
    async def get_database_connection(self):
        """Get database connection with proper error handling"""
        if not self.db_pool:
            raise BotException("Database pool not initialized")
        
        try:
            return self.db_pool.connect(config.database_url)
        except Exception as e:
            logger.error(f"Database connection error: {e}")
            raise BotException(f"Failed to connect to database: {e}")
    
    def add_cleanup_task(self, task):
        """Add a task to be cleaned up on shutdown"""
        self._cleanup_tasks.append(task)
    
    def get_uptime(self) -> timedelta:
        """Get bot uptime"""
        return datetime.now() - self._start_time

# Global bot instance
bot = MemoryOptimizedBot()
