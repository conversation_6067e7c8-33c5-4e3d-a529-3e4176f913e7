"""
Contact Support Configuration View
"""
import discord
from interactions.views.base import BaseView
from embeds.builders import create_success_embed, create_error_embed, InfoEmbed
from utils.database import db_manager
from core.logging import logger
from config.messages import SystemConfig


class ContactSupportConfigView(BaseView):
    """Main view for configuring contact support settings"""
    
    def __init__(self, user_id: int, guild_id: int):
        super().__init__(timeout=SystemConfig.CONFIRMATION_TIMEOUT, user_id=user_id)
        self.guild_id = guild_id
        
    @discord.ui.button(
        label="Contact Info",
        style=discord.ButtonStyle.primary,
        emoji="📝"
    )
    async def configure_contact_info(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Configure contact support information"""
        from interactions.modals import ContactSupportConfigModal

        # Get current configuration to pre-fill modal
        contact_config = await db_manager.get_contact_support_config(interaction.guild.id)

        modal = ContactSupportConfigModal()

        # Pre-fill with current values
        if contact_config['contact_message']:
            modal.contact_message.default = contact_config['contact_message']
        if contact_config['contact_discord_server']:
            modal.discord_server.default = contact_config['contact_discord_server']
        if contact_config['contact_website']:
            modal.website.default = contact_config['contact_website']
        if contact_config['contact_email']:
            modal.email.default = contact_config['contact_email']

        await interaction.response.send_modal(modal)

    @discord.ui.button(
        label="Contact Methods",
        style=discord.ButtonStyle.secondary,
        emoji="📋"
    )
    async def configure_contact_methods(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Configure contact methods list"""
        from interactions.modals import ContactMethodsConfigModal
        from config.messages import ContactMessages

        # Get current configuration to pre-fill modal
        contact_config = await db_manager.get_contact_support_config(interaction.guild.id)

        modal = ContactMethodsConfigModal()

        # Pre-fill with current values or defaults
        if contact_config['contact_methods']:
            modal.methods.default = '\n'.join(contact_config['contact_methods'])
        else:
            modal.methods.default = '\n'.join(ContactMessages.DEFAULT_CONTACT_METHODS)

        await interaction.response.send_modal(modal)

    @discord.ui.button(
        label="Preview",
        style=discord.ButtonStyle.secondary,
        emoji="🔍"
    )
    async def preview_contact_info(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Preview how the contact information will look"""
        from config.messages import ContactMessages

        try:
            # Generate contact info preview
            contact_info = await ContactMessages.get_contact_info(interaction.guild)

            embed = InfoEmbed(
                "Contact Information Preview",
                f"This is how users will see the contact information:\n\n{contact_info}"
            ).build()

            await interaction.response.send_message(embed=embed, ephemeral=True)

        except Exception as e:
            logger.error(f"Error generating contact info preview: {e}")
            embed = create_error_embed(
                "Preview Error",
                "Failed to generate contact information preview. Please try again."
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)

    @discord.ui.button(
        label="Reset to Defaults",
        style=discord.ButtonStyle.danger,
        emoji="🗑️"
    )
    async def reset_to_defaults(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Reset contact support configuration to defaults"""
        try:
            # Clear all custom configuration
            success = await db_manager.update_contact_support_config(
                interaction.guild.id,
                contact_message=None,
                contact_discord_server=None,
                contact_website=None,
                contact_email=None,
                contact_methods=[]
            )

            if success:
                embed = create_success_embed(
                    "Reset to Defaults",
                    "Contact support configuration has been reset to default values.\n\n"
                    "*Users will now see the default contact methods and messages.*"
                )
            else:
                embed = create_error_embed(
                    "Reset Failed",
                    "Failed to reset contact support configuration. Please try again."
                )

            await interaction.response.send_message(embed=embed, ephemeral=True)

        except Exception as e:
            logger.error(f"Error resetting contact support config: {e}")
            embed = create_error_embed(
                "Reset Error",
                "An error occurred while resetting the configuration. Please try again."
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)

    @discord.ui.button(
        label="Back to Dashboard",
        style=discord.ButtonStyle.secondary,
        emoji="🔙",
        row=1
    )
    async def back_to_dashboard(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Return to the main dashboard"""
        # Import here to avoid circular imports
        from commands.slash_commands import ComprehensiveDashboard, DashboardPage

        # Create new dashboard view
        view = ComprehensiveDashboard(interaction.user.id)
        view.current_page = DashboardPage.CONFIGURATION
        view._setup_configuration_section_buttons()

        embed = InfoEmbed(
            "⚙️ Configuration Dashboard",
            f"Welcome back to the configuration section! 🛠️\n\n"
            f"**Available Configuration Options:**\n"
            f"• **Channel Settings** - Configure welcome, activity, and milestone channels\n"
            f"• **Message Counts** - Set activity and milestone thresholds\n"
            f"• **Emoji Settings** - Customize giveaway category emojis\n"
            f"• **Role Permissions** - Configure participant and admin roles\n"
            f"• **Bonus Entries** - Set up role-based bonus entry multipliers\n"
            f"• **Contact Support** - Configure how users can contact admins\n\n"
            f"*Select an option below to configure your server settings* ⚙️"
        ).build()

        if interaction.response.is_done():
            await interaction.followup.edit_message(interaction.message.id, embed=embed, view=view)
        else:
            await interaction.response.edit_message(embed=embed, view=view)
