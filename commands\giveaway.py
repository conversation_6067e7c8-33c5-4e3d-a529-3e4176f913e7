"""
Giveaway commands with memory-optimized views and interactions.
Implements discord.py v2.0+ features with efficient resource management.
"""
import discord
from discord.ext import commands, tasks
import asyncio
import random
from typing import Optional, List
from datetime import datetime, timedelta

from core.logging import logger
from core.config import Colors, Emojis
from embeds.builders import GiveawayEmbed, EmbedTemplates, create_success_embed, create_error_embed
from interactions.views.base import BaseView
from utils.database import db_manager
from config.messages import ButtonLabels, SystemConfig

class ModernGiveawayView(BaseView):
    """Enhanced giveaway view with modern UI components"""
    
    def __init__(self, giveaway_data: dict):
        super().__init__(timeout=SystemConfig.GIVEAWAY_VIEW_TIMEOUT)
        self.giveaway_data = giveaway_data
        self._setup_dynamic_components()

    def _setup_dynamic_components(self):
        """Setup components based on giveaway type and permissions"""
        # Main entry button - will be updated with participant count
        entry_button = discord.ui.Button(
            emoji=Emojis.GIVEAWAY,
            style=discord.ButtonStyle.primary,
            label="🎉 0",  # Will be updated with actual count
            custom_id="enter_giveaway"
        )
        entry_button.callback = self.enter_giveaway
        self.add_item(entry_button)
        
        # Analytics button for admins
        analytics_button = discord.ui.Button(
            emoji="📊", 
            style=discord.ButtonStyle.secondary, 
            label="View Analytics",
            custom_id="view_analytics",
            row=1
        )
        analytics_button.callback = self.view_analytics
        self.add_item(analytics_button)

        # Share button for viral growth
        share_button = discord.ui.Button(
            emoji="🔗",
            style=discord.ButtonStyle.secondary,
            label="Share Giveaway",
            custom_id="share_giveaway",
            row=1
        )
        share_button.callback = self.share_giveaway
        self.add_item(share_button)

        # Update button with current participant count
        asyncio.create_task(self._update_entry_button())

    async def _update_entry_button(self):
        """Update the entry button with current participant count"""
        try:
            if self.giveaway_data.get('message_id', 0) > 0:
                participants = await db_manager.get_giveaway_participants_with_entries(
                    self.giveaway_data['message_id']
                )
                participant_count = len(participants)

                # Update the entry button label
                for item in self.children:
                    if hasattr(item, 'custom_id') and item.custom_id == "enter_giveaway":
                        item.label = f"🎉 {participant_count}"
                        break
        except Exception as e:
            logger.error(f"Error updating entry button: {e}")

    async def enter_giveaway(self, interaction: discord.Interaction):
        """Handle giveaway entry"""
        try:
            # Check if user can participate in giveaways
            from utils.permissions import permission_manager
            if not await permission_manager.can_participate_in_giveaways(interaction.user):
                # Import the warning view and configuration
                from interactions.authorization_views import GiveawayWarningView
                from config.messages import GiveawayMessages

                # Create warning embed using configuration
                embed = create_error_embed(
                    GiveawayMessages.WARNING_TITLE,
                    GiveawayMessages.get_warning_message(is_quick=False)
                )

                # Create view with quick authorization and contact buttons
                view = GiveawayWarningView(interaction.user.id, "authorization")
                await interaction.response.send_message(embed=embed, view=view, ephemeral=True)
                return

            # Calculate bonus entries for the user
            bonus_info = await permission_manager.get_user_bonus_info(interaction.user)
            total_entries = bonus_info['total_entries']
            
            # Add participant to giveaway with bonus entries
            success = await db_manager.add_giveaway_participant(
                self.giveaway_data['message_id'],
                interaction.user.id,
                total_entries
            )

            if success:
                # Create success message with bonus entry information
                from config.messages import GiveawayMessages

                if total_entries > 1:
                    bonus_text = f"\n\n{GiveawayMessages.BONUS_ENTRY_DETAILS.format(entries=total_entries)}"
                    if bonus_info['highest_role']:
                        bonus_text += f"\nThanks to your **{bonus_info['highest_role'].name}** role!"
                else:
                    bonus_text = f"\n\n{GiveawayMessages.NO_BONUS_MESSAGE}"

                embed = create_success_embed(
                    "✅ Entered Giveaway!",
                    f"You've successfully entered the giveaway for **{self.giveaway_data['title']}**!{bonus_text}"
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
                logger.debug(f"User {interaction.user} entered giveaway {self.giveaway_data['message_id']} with {total_entries} entries")

                # Update the entry button with new participant count
                await self._update_entry_button()
                try:
                    await interaction.edit_original_response(view=self)
                except:
                    # If we can't edit the original message, that's okay
                    pass
            else:
                # Get current entries for already entered message
                participants_data = await db_manager.get_giveaway_participants_with_entries(self.giveaway_data['message_id'])
                current_entries = participants_data.get(str(interaction.user.id), 1)

                embed = create_error_embed(
                    "Already Entered",
                    f"You're already participating in this giveaway with **{current_entries} entries**!"
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
                
        except Exception as e:
            logger.error(f"Error in giveaway entry: {e}")
            embed = create_error_embed(
                "Error",
                "An error occurred while entering the giveaway. Please try again."
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)

    async def view_analytics(self, interaction: discord.Interaction):
        """Show giveaway analytics to admins"""
        try:
            from utils.permissions import permission_manager
            if not await permission_manager.has_bot_admin_access(interaction.user):
                await interaction.response.send_message("❌ Admin access required.", ephemeral=True)
                return
            
            # Get analytics data
            participants_data = await db_manager.get_giveaway_participants_with_entries(self.giveaway_data['message_id'])
            total_participants = len(participants_data)
            total_entries = sum(int(entries) for entries in participants_data.values())
            
            # Calculate engagement metrics
            guild_member_count = interaction.guild.member_count
            engagement_rate = (total_participants / guild_member_count * 100) if guild_member_count > 0 else 0
            
            analytics_embed = discord.Embed(
                title="📊 Giveaway Analytics",
                description=f"**{self.giveaway_data['title']}**",
                color=0x3498db
            )
            analytics_embed.add_field(name="👥 Participants", value=total_participants, inline=True)
            analytics_embed.add_field(name="🎯 Total Entries", value=total_entries, inline=True)
            analytics_embed.add_field(name="📈 Engagement", value=f"{engagement_rate:.1f}%", inline=True)
            analytics_embed.add_field(name="⚡ Avg Entries/User", value=f"{total_entries/total_participants:.1f}" if total_participants > 0 else "0", inline=True)
            
            await interaction.response.send_message(embed=analytics_embed, ephemeral=True)
            
        except Exception as e:
            logger.error(f"Error showing analytics: {e}")
            await interaction.response.send_message("❌ Error loading analytics.", ephemeral=True)

    async def share_giveaway(self, interaction: discord.Interaction):
        """Generate shareable giveaway link"""
        try:
            # Create shareable content
            share_text = f"🎉 **Amazing Giveaway Alert!** 🎉\n\n" \
                        f"**Prize:** {self.giveaway_data['title']}\n" \
                        f"**Server:** {interaction.guild.name}\n\n" \
                        f"Join here: https://discord.gg/{interaction.guild.vanity_url_code or 'invite'}\n" \
                        f"Use `/giveaway` to see active giveaways!\n\n" \
                        f"#DiscordGiveaway #Free"
            
            share_embed = discord.Embed(
                title="🔗 Share This Giveaway",
                description="Copy the text below to share on social media:",
                color=0x00ff00
            )
            share_embed.add_field(name="📋 Copy This Text:", value=f"```{share_text}```", inline=False)
            share_embed.add_field(name="💡 Pro Tip:", value="Share on Twitter, Reddit, or Discord servers to boost entries!", inline=False)
            
            await interaction.response.send_message(embed=share_embed, ephemeral=True)
            
        except Exception as e:
            logger.error(f"Error generating share content: {e}")
            await interaction.response.send_message("❌ Error generating share link.", ephemeral=True)

class QuickGiveawayView(BaseView):
    """Quick giveaway view - first authorized member to react wins"""

    def __init__(self, giveaway_data: dict):
        super().__init__(timeout=None)  # No timeout for giveaways
        self.giveaway_data = giveaway_data
        self.winner_found = False

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        """Check if user can interact with the quick giveaway"""
        return True  # Allow all users to try

    @discord.ui.button(label="⚡ React to Win!", style=discord.ButtonStyle.primary, emoji="⚡", custom_id="quick_enter")
    async def quick_enter(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Handle quick giveaway entry - first authorized user wins"""
        try:
            # Check if giveaway is still active
            if self.winner_found:
                await interaction.response.send_message(
                    "⚡ This quick giveaway has already ended!",
                    ephemeral=True
                )
                return

            # Check if giveaway has expired
            from datetime import datetime
            if datetime.now() > self.giveaway_data['end_time']:
                await interaction.response.send_message(
                    "⏰ This quick giveaway has expired!",
                    ephemeral=True
                )
                return

            # Check if user can participate in giveaways
            from utils.permissions import permission_manager
            if not await permission_manager.can_participate_in_giveaways(interaction.user):
                # Import the warning view and configuration
                from interactions.authorization_views import GiveawayWarningView
                from config.messages import GiveawayMessages

                # Create warning embed for quick giveaway using configuration
                embed = create_error_embed(
                    GiveawayMessages.QUICK_WARNING_TITLE,
                    GiveawayMessages.get_warning_message(is_quick=True)
                )

                # Create view with quick authorization and contact buttons
                view = GiveawayWarningView(interaction.user.id, "authorization")
                await interaction.response.send_message(embed=embed, view=view, ephemeral=True)
                return

            # Calculate bonus entries for the user (for display purposes)
            bonus_info = await permission_manager.get_user_bonus_info(interaction.user)
            total_entries = bonus_info['total_entries']

            # Check additional requirements if any
            requirements = self.giveaway_data.get('requirements')
            if requirements:
                # For now, just show the requirements - could add specific checks later
                pass

            # User wins! (First authorized user to react)
            self.winner_found = True

            # Update the embed to show winner
            original_embed = interaction.message.embeds[0]
            winner_embed = GiveawayEmbed(
                "🎉 QUICK GIVEAWAY WINNER! 🎉",
                f"{original_embed.description}\n\n"
                f"🏆 **WINNER:** {interaction.user.mention}\n"
                f"⚡ **First authorized member to react!**\n\n"
                f"Congratulations! 🎊"
            ).build()

            # Disable the button
            button.disabled = True
            button.label = "✅ Winner Found!"
            button.style = discord.ButtonStyle.success

            # Update the message
            await interaction.response.edit_message(embed=winner_embed, view=self)

            # Send winner notification
            try:
                prize_name = self.giveaway_data['title'].replace("⚡ Quick: ", "")
                dm_embed = create_success_embed(
                    "🎉 You Won a Quick Giveaway!",
                    f"Congratulations! You won: **{prize_name}**\n\n"
                    f"You were the first authorized member to react!\n"
                    f"Server: **{interaction.guild.name}**\n\n"
                    f"The giveaway host will contact you soon about claiming your prize."
                )
                await interaction.user.send(embed=dm_embed)
            except discord.Forbidden:
                # User has DMs disabled
                pass

            # Log the win
            logger.info(f"Quick giveaway won by {interaction.user} in {interaction.guild.name}")

            # Update database to mark as ended
            await db_manager.end_giveaway(self.giveaway_data['message_id'], [interaction.user.id])

        except Exception as e:
            logger.error(f"Error in quick giveaway entry: {e}")
            await interaction.response.send_message("❌ Something went wrong!", ephemeral=True)


def setup_giveaway_commands(bot: commands.Bot):
    """Setup giveaway-related commands"""
    
    @bot.command(name='giveaway')
    @commands.has_permissions(manage_messages=True)
    async def create_giveaway(ctx, duration: str, winners: int, *, prize):
        """Create a giveaway. Format: !giveaway 1h 1 Amazing Prize"""
        
        # Parse duration
        from config.messages import SystemConfig
        duration_seconds = 0

        try:
            if duration[-1].lower() in SystemConfig.TIME_UNITS:
                duration_seconds = int(duration[:-1]) * SystemConfig.TIME_UNITS[duration[-1].lower()]
            else:
                duration_seconds = int(duration) * 60  # Default to minutes
        except (ValueError, IndexError):
            embed = create_error_embed(
                "Invalid Duration",
                "Use format: 30s, 5m, 2h, 1d"
            )
            await ctx.send(embed=embed)
            return
        
        if duration_seconds < 10:
            embed = create_error_embed(
                "Duration Too Short",
                "Minimum duration is 10 seconds."
            )
            await ctx.send(embed=embed)
            return
        
        if winners < 1 or winners > 20:
            embed = create_error_embed(
                "Invalid Winner Count",
                "Winner count must be between 1 and 20."
            )
            await ctx.send(embed=embed)
            return
        
        end_time = datetime.now() + timedelta(seconds=duration_seconds)
        
        # Check if there are any bonus roles configured
        role_multipliers = await db_manager.get_role_bonus_multipliers(ctx.guild.id)
        multipliers_list = []
        if role_multipliers:
            for role_id, multiplier in role_multipliers.items():
                role = ctx.guild.get_role(int(role_id))
                if role:
                    multipliers_list.append(f"{role.mention}: +{multiplier} entries")
        
        # Calculate duration text
        duration_days = duration_seconds // 86400
        duration_hours = (duration_seconds % 86400) // 3600
        duration_minutes = (duration_seconds % 3600) // 60
        
        if duration_days > 0:
            duration_text = f"{duration_days} day{'s' if duration_days != 1 else ''}"
            if duration_hours > 0:
                remaining_hours = duration_hours
                duration_text += f" (Ends in {duration_days} day{'s' if duration_days != 1 else ''} {remaining_hours} hour{'s' if remaining_hours != 1 else ''})"
            else:
                duration_text += f" (Ends in {duration_days} day{'s' if duration_days != 1 else ''})"
        elif duration_hours > 0:
            duration_text = f"{duration_hours} hour{'s' if duration_hours != 1 else ''}"
            if duration_minutes > 0:
                duration_text += f" (Ends in {duration_hours}h {duration_minutes}m)"
            else:
                duration_text += f" (Ends in {duration_hours} hour{'s' if duration_hours != 1 else ''})"
        else:
            duration_text = f"{duration_minutes} minute{'s' if duration_minutes != 1 else ''} (Ends in {duration_minutes}m)"

        # Generate unique giveaway ID
        import time
        giveaway_id = str(int(time.time() * 1000))  # Timestamp-based ID
        
        # Create professional giveaway embed
        embed = GiveawayEmbed.create_professional_giveaway(
            prize=prize,
            duration_text=duration_text,
            host_name=f"@{ctx.author.display_name}",
            winner_count=winners,
            end_time=end_time,
            giveaway_id=giveaway_id,
            multipliers=multipliers_list
        )
        
        # Create giveaway data
        giveaway_data = {
            'message_id': 0,  # Will be set after sending
            'guild_id': ctx.guild.id,
            'channel_id': ctx.channel.id,
            'title': prize,
            'description': embed.description,
            'end_time': end_time,
            'winner_count': winners,
            'created_by': ctx.author.id
        }
        
        # Create modern enhanced view
        view = ModernGiveawayView(giveaway_data)
        
        # Send message
        message = await ctx.send(embed=embed, view=view)

        # Update giveaway data with message ID
        giveaway_data['message_id'] = message.id
        view.giveaway_data['message_id'] = message.id
        
        # Save to database
        success = await db_manager.create_giveaway(
            message.id, ctx.guild.id, ctx.channel.id,
            prize, embed.description, end_time, winners, ctx.author.id
        )
        
        if success:
            # Register view for cleanup
            bot.register_view(f"giveaway_{message.id}", view)
            logger.info(f"Giveaway created: {prize} by {ctx.author}")
        else:
            embed = create_error_embed(
                "Database Error",
                "Failed to save giveaway to database."
            )
            await ctx.send(embed=embed)
    
    @bot.command(name='quick_giveaway', aliases=['qg'])
    @commands.has_permissions(manage_messages=True)
    async def quick_giveaway(ctx, duration: str = "30s", winners: int = 1, *, prize: str = None):
        """Start a quick giveaway with custom duration and winners"""

        if not prize:
            from config.messages import SystemConfig
            prize = random.choice(SystemConfig.QUICK_GIVEAWAY_PRIZES)

        # Parse duration
        duration_seconds = 30  # Default

        try:
            if duration[-1].lower() in SystemConfig.TIME_UNITS:
                duration_seconds = int(duration[:-1]) * SystemConfig.TIME_UNITS[duration[-1].lower()]
            else:
                duration_seconds = int(duration)
        except (ValueError, IndexError):
            duration_seconds = 30  # Default on error

        # Validate winners
        if winners < 1 or winners > 20:
            embed = create_error_embed(
                "Invalid Winner Count",
                "Winner count must be between 1 and 20."
            )
            await ctx.send(embed=embed)
            return

        end_time = datetime.now() + timedelta(seconds=duration_seconds)

        # Format duration for display
        if duration_seconds < 60:
            duration_text = f"{duration_seconds} seconds"
        elif duration_seconds < 3600:
            duration_text = f"{duration_seconds // 60} minutes"
        elif duration_seconds < 86400:
            duration_text = f"{duration_seconds // 3600} hours"
        else:
            duration_text = f"{duration_seconds // 86400} days"

        # Check if there are any bonus roles configured
        role_multipliers = await db_manager.get_role_bonus_multipliers(ctx.guild.id)
        bonus_info_text = ""
        if role_multipliers:
            from config.messages import GiveawayMessages
            bonus_info_text = f"\n{GiveawayMessages.BONUS_ENTRY_INFO}"

        embed = GiveawayEmbed(
            "⚡ QUICK GIVEAWAY! ⚡",
            f"**Prize:** {prize}\n"
            f"**Winners:** {winners}\n"
            f"**Duration:** {duration_text}\n"
            f"**Ends:** <t:{int(end_time.timestamp())}:R>\n\n"
            f"Click the button below to enter!\n"
            f"⚠️ **Requirement:** You must be authorized.{bonus_info_text}"
        ).set_footer(f"Created by {ctx.author.display_name}").build()

        # Create giveaway data
        giveaway_data = {
            'message_id': 0,  # Will be set after sending
            'guild_id': ctx.guild.id,
            'channel_id': ctx.channel.id,
            'title': prize,
            'description': embed.description,
            'end_time': end_time,
            'winner_count': winners,
            'created_by': ctx.author.id
        }

        # Create modern enhanced view
        view = ModernGiveawayView(giveaway_data)

        # Send message
        message = await ctx.send(embed=embed, view=view)

        # Update giveaway data with message ID
        giveaway_data['message_id'] = message.id
        view.giveaway_data['message_id'] = message.id

        # Save to database
        success = await db_manager.create_giveaway(
            message.id, ctx.guild.id, ctx.channel.id,
            prize, embed.description, end_time, winners, ctx.author.id
        )

        if success:
            # Register view for cleanup
            bot.register_view(f"quick_giveaway_{message.id}", view)
            logger.info(f"Quick giveaway created: {prize} by {ctx.author}")
        else:
            embed = create_error_embed(
                "Database Error",
                "Failed to save giveaway to database."
            )
            await ctx.send(embed=embed)
    
    @tasks.loop(minutes=1)
    async def check_giveaways():
        """Check for expired giveaways"""
        try:
            expired_giveaways = await db_manager.get_expired_giveaways()
            
            for giveaway in expired_giveaways:
                await end_giveaway(giveaway)
                await db_manager.delete_giveaway(giveaway['message_id'])
                
        except Exception as e:
            logger.error(f"Error checking giveaways: {e}")
    
    async def end_giveaway(giveaway_data: dict):
        """End a giveaway and select winners"""
        try:
            channel = bot.get_channel(giveaway_data['channel_id'])
            if not channel:
                return
            
            try:
                message = await channel.fetch_message(giveaway_data['message_id'])
            except discord.NotFound:
                return
            
            # Get participants with entries
            participants_data = await db_manager.get_giveaway_participants_with_entries(giveaway_data['message_id'])

            if not participants_data:
                embed = EmbedTemplates.giveaway_ended_no_participants()
                await channel.send(embed=embed)
                return

            # Create weighted list for selection based on entries
            weighted_participants = []
            for user_id_str, entries in participants_data.items():
                user_id = int(user_id_str)
                # Add user_id multiple times based on their entries
                weighted_participants.extend([user_id] * int(entries))

            # Select winners from weighted list
            winner_count = min(giveaway_data['winner_count'], len(set(weighted_participants)))
            winners = []
            selected_users = set()

            # Ensure we don't select the same user multiple times
            for _ in range(winner_count):
                if len(selected_users) >= len(participants_data):
                    break

                while True:
                    winner_id = random.choice(weighted_participants)
                    if winner_id not in selected_users:
                        winners.append(winner_id)
                        selected_users.add(winner_id)
                        break
            
            # Get winner mentions
            winner_mentions = []
            for winner_id in winners:
                try:
                    user = await bot.fetch_user(winner_id)
                    winner_mentions.append(user.mention)
                except:
                    continue
            
            if winner_mentions:
                # Get entry information for display
                winner_info = []
                total_entries = sum(int(entries) for entries in participants_data.values())

                for winner_id in winners:
                    try:
                        user = await bot.fetch_user(winner_id)
                        winner_entries = participants_data.get(str(winner_id), 1)
                        winner_info.append(f"{user.mention} ({winner_entries} entries)")
                    except:
                        continue

                winner_text = '\n'.join(winner_info) if len(winner_info) > 1 else winner_info[0] if winner_info else "Unknown"

                embed = create_success_embed(
                    "🎉 Giveaway Results!",
                    f"**Prize:** {giveaway_data['title']}\n\n"
                    f"**Selected Winner(s):**\n{winner_text}\n\n"
                    f"📊 **Total Participants:** {len(participants_data)}\n"
                    f"🎯 **Total Entries:** {total_entries}\n\n"
                    f"Winners will be contacted shortly with details! 🎊"
                )
                await channel.send(embed=embed)

                # Send DM to winners
                for winner_id in winners:
                    try:
                        user = await bot.fetch_user(winner_id)
                        winner_dm = create_success_embed(
                            "🎉 Giveaway Winner!",
                            f"Congratulations! You've won: **{giveaway_data['title']}**\n\n"
                            f"🎊 You were selected from {len(participants)} participants!\n"
                            f"📧 You'll receive further instructions soon.\n\n"
                            f"Thank you for participating in our community events!"
                        )
                        await user.send(embed=winner_dm)
                    except:
                        continue
                
                # Update original message
                try:
                    ended_embed = GiveawayEmbed(
                        "🎉 GIVEAWAY ENDED! 🎉",
                        f"**Prize:** {giveaway_data['title']}\n"
                        f"**Winners:** {' '.join(winner_mentions)}\n\n"
                        f"This giveaway has ended."
                    ).build()
                    
                    # Disable view
                    view = discord.ui.View()
                    button = discord.ui.Button(
                        emoji=Emojis.GIVEAWAY,
                        style=discord.ButtonStyle.secondary,
                        label="Giveaway Ended",
                        disabled=True
                    )
                    view.add_item(button)
                    
                    await message.edit(embed=ended_embed, view=view)
                except:
                    pass
                
                logger.info(f"Giveaway ended: {giveaway_data['title']} - {len(winner_mentions)} winners")
            else:
                embed = create_error_embed(
                    "Giveaway Ended",
                    f"**Prize:** {giveaway_data['title']}\n\n"
                    f"Could not contact the selected winners."
                )
                await channel.send(embed=embed)
                
        except Exception as e:
            logger.error(f"Error ending giveaway: {e}")



    # Start the giveaway checker task
    if not check_giveaways.is_running():
        check_giveaways.start()
        logger.info("Giveaway checker task started")

    logger.info("Giveaway commands loaded")
