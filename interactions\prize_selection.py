"""
Prize selection system with preset options and custom emojis.
Handles special prize types like Grow A Garden items.
"""
import discord
from typing import Optional

from core.logging import logger
from embeds.builders import create_success_embed, create_error_embed, GiveawayEmbed
from utils.database import db_manager
from interactions.emoji_config import EmojiManager
from datetime import datetime, timedelta

class PrizeSelectionView(discord.ui.View):
    """View for selecting prize types"""

    def __init__(self, user_id: int, guild_id: int):
        super().__init__(timeout=300.0)
        self.user_id = user_id
        self.guild_id = guild_id
        self.selected_prize = None

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        """Check if the user can interact with this view"""
        if interaction.user.id != self.user_id:
            await interaction.response.send_message(
                "❌ This prize selection is not for you!",
                ephemeral=True
            )
            return False
        return True

    @classmethod
    async def create(cls, user_id: int, guild_id: int):
        """Create a PrizeSelectionView with dynamic emojis"""
        view = cls(user_id, guild_id)
        await view._setup_buttons()
        return view

    async def _setup_buttons(self):
        """Setup buttons with dynamic emojis from database"""
        try:
            # Get configured emojis for this guild
            emojis = await EmojiManager.get_guild_emojis(self.guild_id)

            # Create buttons with dynamic emojis
            nitro_button = discord.ui.Button(
                label="Discord Nitro",
                style=discord.ButtonStyle.primary,
                emoji=emojis['nitro'],
                row=0
            )
            nitro_button.callback = self._nitro_callback

            robux_button = discord.ui.Button(
                label="Robux",
                style=discord.ButtonStyle.success,
                emoji=emojis['robux'],
                row=0
            )
            robux_button.callback = self._robux_callback

            garden_button = discord.ui.Button(
                label="Grow A Garden Item",
                style=discord.ButtonStyle.secondary,
                emoji=emojis['garden'],
                row=0
            )
            garden_button.callback = self._garden_callback

            custom_button = discord.ui.Button(
                label="Custom Prize",
                style=discord.ButtonStyle.secondary,
                emoji=emojis['custom'],
                row=1
            )
            custom_button.callback = self._custom_callback

            # Add buttons to view
            self.add_item(nitro_button)
            self.add_item(robux_button)
            self.add_item(garden_button)
            self.add_item(custom_button)

        except Exception as e:
            logger.error(f"Error setting up prize selection buttons: {e}")
            # Fallback to default buttons if there's an error
            await self._setup_fallback_buttons()

    async def _setup_fallback_buttons(self):
        """Setup fallback buttons with default emojis"""
        nitro_button = discord.ui.Button(
            label="Discord Nitro",
            style=discord.ButtonStyle.primary,
            emoji="💎",
            row=0
        )
        nitro_button.callback = self._nitro_callback

        robux_button = discord.ui.Button(
            label="Robux",
            style=discord.ButtonStyle.success,
            emoji="🔶",
            row=0
        )
        robux_button.callback = self._robux_callback

        garden_button = discord.ui.Button(
            label="Grow A Garden Item",
            style=discord.ButtonStyle.secondary,
            emoji="🌳",
            row=0
        )
        garden_button.callback = self._garden_callback

        custom_button = discord.ui.Button(
            label="Custom Prize",
            style=discord.ButtonStyle.secondary,
            emoji="🎁",
            row=1
        )
        custom_button.callback = self._custom_callback

        # Add buttons to view
        self.add_item(nitro_button)
        self.add_item(robux_button)
        self.add_item(garden_button)
        self.add_item(custom_button)

    async def _nitro_callback(self, interaction: discord.Interaction):
        """Select Discord Nitro as prize"""
        modal = NitroPrizeModal(self.guild_id)
        await interaction.response.send_modal(modal)

    async def _robux_callback(self, interaction: discord.Interaction):
        """Select Robux as prize"""
        modal = RobuxPrizeModal(self.guild_id)
        await interaction.response.send_modal(modal)

    async def _garden_callback(self, interaction: discord.Interaction):
        """Select Grow A Garden item as prize"""
        modal = GardenPrizeModal(self.guild_id)
        await interaction.response.send_modal(modal)

    async def _custom_callback(self, interaction: discord.Interaction):
        """Select custom prize"""
        modal = CustomPrizeModal(self.guild_id)
        await interaction.response.send_modal(modal)

class NitroPrizeModal(discord.ui.Modal):
    """Modal for Discord Nitro prizes"""

    def __init__(self, guild_id: int):
        super().__init__(title="Discord Nitro Giveaway")
        self.guild_id = guild_id
        
        self.nitro_type = discord.ui.TextInput(
            label="Nitro Type",
            placeholder="Basic, Classic, or specify duration (e.g., '1 month', '3 months')",
            max_length=50,
            required=True
        )
        
        self.duration = discord.ui.TextInput(
            label="Giveaway Duration",
            placeholder="How long? (e.g., 1h, 30m, 2d, 1w)",
            max_length=10,
            required=True
        )
        
        self.winners = discord.ui.TextInput(
            label="Number of Winners",
            placeholder="How many winners? (1-20)",
            max_length=2,
            required=True
        )
        
        self.description = discord.ui.TextInput(
            label="Additional Details (Optional)",
            placeholder="Any special requirements or details...",
            style=discord.TextStyle.paragraph,
            max_length=500,
            required=False
        )
        
        self.add_item(self.nitro_type)
        self.add_item(self.duration)
        self.add_item(self.winners)
        self.add_item(self.description)
    
    async def on_submit(self, interaction: discord.Interaction):
        """Handle Nitro giveaway creation"""
        prize = await EmojiManager.format_prize(self.guild_id, 'nitro', f"Discord Nitro {self.nitro_type.value}")
        await self._create_giveaway(interaction, prize)
    
    async def _create_giveaway(self, interaction: discord.Interaction, prize: str):
        """Create the giveaway with the specified prize"""
        try:
            # Parse duration
            time_units = {'s': 1, 'm': 60, 'h': 3600, 'd': 86400, 'w': 604800}
            duration_seconds = 0
            
            duration_str = self.duration.value.lower()
            try:
                if duration_str[-1] in time_units:
                    duration_seconds = int(duration_str[:-1]) * time_units[duration_str[-1]]
                else:
                    duration_seconds = int(duration_str) * 60  # Default to minutes
            except (ValueError, IndexError):
                embed = create_error_embed(
                    "Invalid Duration",
                    "Please use format: 30s, 5m, 2h, 1d, 1w"
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
                return
            
            if duration_seconds < 10:
                embed = create_error_embed(
                    "Duration Too Short",
                    "Minimum duration is 10 seconds."
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
                return
            
            # Parse winners
            try:
                winner_count = int(self.winners.value)
                if winner_count < 1 or winner_count > 20:
                    raise ValueError()
            except ValueError:
                embed = create_error_embed(
                    "Invalid Winner Count",
                    "Please enter a number between 1 and 20."
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
                return
            
            # Create giveaway
            end_time = datetime.now() + timedelta(seconds=duration_seconds)
            
            # Get bonus role multipliers  
            from utils.database import db_manager
            role_multipliers = await db_manager.get_role_bonus_multipliers(interaction.guild.id)
            multipliers_list = []
            if role_multipliers:
                for role_id, multiplier in role_multipliers.items():
                    role = interaction.guild.get_role(int(role_id))
                    if role:
                        multipliers_list.append(f"{role.mention}: +{multiplier} entries")
            
            # Calculate duration text
            # duration_seconds is already calculated above from user input
            duration_days = duration_seconds // 86400
            duration_hours = (duration_seconds % 86400) // 3600
            duration_minutes = (duration_seconds % 3600) // 60
            
            if duration_days > 0:
                duration_text = f"{duration_days} day{'s' if duration_days != 1 else ''}"
                duration_text += f" (Ends in {duration_days} day{'s' if duration_days != 1 else ''})"
            elif duration_hours > 0:
                duration_text = f"{duration_hours} hour{'s' if duration_hours != 1 else ''}"
                duration_text += f" (Ends in {duration_hours} hour{'s' if duration_hours != 1 else ''})"
            else:
                duration_text = f"{duration_minutes} minute{'s' if duration_minutes != 1 else ''} (Ends in {duration_minutes}m)"

            # Generate unique giveaway ID
            import time
            giveaway_id = str(int(time.time() * 1000))  # Timestamp-based ID
            
            # Add description if provided
            prize_text = prize
            if self.description.value:
                prize_text += f"\n*{self.description.value}*"
            
            # Create professional giveaway embed
            embed = GiveawayEmbed.create_professional_giveaway(
                prize=prize_text,
                duration_text=duration_text,
                host_name=f"@{interaction.user.display_name}",
                winner_count=winner_count,
                end_time=end_time,
                giveaway_id=giveaway_id,
                multipliers=multipliers_list
            )
            
            # Send giveaway with button view
            from commands.giveaway import ModernGiveawayView

            # Create giveaway data for the view
            giveaway_data = {
                'message_id': 0,  # Will be set after sending
                'guild_id': interaction.guild.id,
                'channel_id': interaction.channel.id,
                'title': prize,
                'description': embed.description,
                'end_time': end_time,
                'winner_count': winner_count,
                'created_by': interaction.user.id
            }

            view = ModernGiveawayView(giveaway_data)
            await interaction.response.send_message(embed=embed, view=view)
            message = await interaction.original_response()
            
            # Add 🎉 reaction for entry
            try:
                await message.add_reaction("🎉")
            except Exception as e:
                logger.warning(f"Failed to add reaction to giveaway: {e}")

            # Update giveaway data with message ID
            giveaway_data['message_id'] = message.id
            view.giveaway_data['message_id'] = message.id
            
            # Save to database
            success = await db_manager.create_giveaway(
                message.id, interaction.guild.id, interaction.channel.id,
                prize, giveaway_desc, end_time, winner_count, interaction.user.id
            )
            
            if success:
                logger.info(f"Nitro giveaway created: {prize} by {interaction.user}")
            else:
                logger.error(f"Failed to save giveaway to database")
                
        except Exception as e:
            logger.error(f"Error in nitro giveaway creation: {e}")
            embed = create_error_embed(
                "Error Creating Giveaway",
                "Something went wrong. Please try again."
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)

class RobuxPrizeModal(discord.ui.Modal):
    """Modal for Robux prizes"""

    def __init__(self, guild_id: int):
        super().__init__(title="Robux Giveaway")
        self.guild_id = guild_id
        
        self.robux_amount = discord.ui.TextInput(
            label="Robux Amount",
            placeholder="How much Robux? (e.g., 100, 500, 1000)",
            max_length=10,
            required=True
        )
        
        self.duration = discord.ui.TextInput(
            label="Giveaway Duration",
            placeholder="How long? (e.g., 1h, 30m, 2d, 1w)",
            max_length=10,
            required=True
        )
        
        self.winners = discord.ui.TextInput(
            label="Number of Winners",
            placeholder="How many winners? (1-20)",
            max_length=2,
            required=True
        )
        
        self.description = discord.ui.TextInput(
            label="Additional Details (Optional)",
            placeholder="Any special requirements or details...",
            style=discord.TextStyle.paragraph,
            max_length=500,
            required=False
        )
        
        self.add_item(self.robux_amount)
        self.add_item(self.duration)
        self.add_item(self.winners)
        self.add_item(self.description)
    
    async def on_submit(self, interaction: discord.Interaction):
        """Handle Robux giveaway creation"""
        try:
            amount = int(self.robux_amount.value.replace(',', ''))
            prize = await EmojiManager.format_prize(self.guild_id, 'robux', f"{amount:,} Robux")
        except ValueError:
            embed = create_error_embed(
                "Invalid Robux Amount",
                "Please enter a valid number for Robux amount."
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        await NitroPrizeModal._create_giveaway(self, interaction, prize)

class GardenPrizeModal(discord.ui.Modal):
    """Modal for Grow A Garden items"""

    def __init__(self, guild_id: int):
        super().__init__(title="Grow A Garden Giveaway")
        self.guild_id = guild_id
        
        self.item_type = discord.ui.TextInput(
            label="Item Type",
            placeholder="Pet or Fruit?",
            max_length=10,
            required=True
        )
        
        self.item_name = discord.ui.TextInput(
            label="Item Name",
            placeholder="What specific pet or fruit? (e.g., Dragon Fruit, Golden Apple)",
            max_length=50,
            required=True
        )
        
        self.quantity = discord.ui.TextInput(
            label="Quantity per Winner",
            placeholder="How many per winner? (e.g., 1, 2, 5)",
            max_length=5,
            required=True
        )

        self.value = discord.ui.TextInput(
            label="Value per Item (Optional)",
            placeholder="Value in sheckles (e.g., 105t, 50b, 1q) - leave blank if no value",
            max_length=20,
            required=False
        )

        self.duration = discord.ui.TextInput(
            label="Giveaway Duration",
            placeholder="How long? (e.g., 1h, 30m, 2d, 1w)",
            max_length=10,
            required=True
        )

        self.winners = discord.ui.TextInput(
            label="Number of Winners",
            placeholder="How many winners? (1-20)",
            max_length=2,
            required=True
        )
        
        self.add_item(self.item_type)
        self.add_item(self.item_name)
        self.add_item(self.quantity)
        self.add_item(self.value)
        self.add_item(self.duration)
        self.add_item(self.winners)
    
    async def on_submit(self, interaction: discord.Interaction):
        """Handle Garden giveaway creation"""
        try:
            quantity = int(self.quantity.value)
            item_type = self.item_type.value.lower()
            
            if item_type not in ['pet', 'fruit']:
                embed = create_error_embed(
                    "Invalid Item Type",
                    "Please specify either 'Pet' or 'Fruit'."
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
                return
            
            # Format the prize with value
            item_name = self.item_name.value
            value_text = ""

            # Add value if provided
            if self.value.value:
                value_text = f" (Worth {self.value.value} sheckles each)"

            # Format based on quantity
            if quantity == 1:
                prize_text = f"{item_name} ({item_type.title()}){value_text}"
            else:
                prize_text = f"{quantity}x {item_name} ({item_type.title()}s){value_text}"

            prize = await EmojiManager.format_prize(self.guild_id, 'garden', prize_text)
                
        except ValueError:
            embed = create_error_embed(
                "Invalid Quantity",
                "Please enter a valid number for quantity."
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return
        
        await NitroPrizeModal._create_giveaway(self, interaction, prize)

class CustomPrizeModal(discord.ui.Modal):
    """Modal for custom prizes"""

    def __init__(self, guild_id: int):
        super().__init__(title="Custom Giveaway")
        self.guild_id = guild_id
        
        self.prize = discord.ui.TextInput(
            label="Prize",
            placeholder="What are you giving away?",
            max_length=200,
            required=True
        )
        
        self.duration = discord.ui.TextInput(
            label="Giveaway Duration",
            placeholder="How long? (e.g., 1h, 30m, 2d, 1w)",
            max_length=10,
            required=True
        )
        
        self.winners = discord.ui.TextInput(
            label="Number of Winners",
            placeholder="How many winners? (1-20)",
            max_length=2,
            required=True
        )
        
        self.description = discord.ui.TextInput(
            label="Description (Optional)",
            placeholder="Additional details about the giveaway...",
            style=discord.TextStyle.paragraph,
            max_length=500,
            required=False
        )
        
        self.add_item(self.prize)
        self.add_item(self.duration)
        self.add_item(self.winners)
        self.add_item(self.description)
    
    async def on_submit(self, interaction: discord.Interaction):
        """Handle custom giveaway creation"""
        prize = await EmojiManager.format_prize(self.guild_id, 'custom', self.prize.value)
        await NitroPrizeModal._create_giveaway(self, interaction, prize)
