"""
Advanced OAuth Token Management System
Implements automatic token refresh, validation, and analytics
"""
import asyncio
import aiohttp
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from utils.database import db_manager
from config.messages import AuthorizationConfig

logger = logging.getLogger(__name__)

class AdvancedTokenManager:
    """Enhanced OAuth token management with auto-refresh and analytics"""
    
    def __init__(self):
        self.refresh_interval = 3600  # Check every hour
        self.cleanup_task = None
        self.session = None
    
    async def start_monitoring(self):
        """Start the token monitoring and refresh service"""
        if not self.session:
            self.session = aiohttp.ClientSession()
        
        self.cleanup_task = asyncio.create_task(self._monitor_tokens())
        logger.info("Advanced OAuth token monitoring started")
    
    async def stop_monitoring(self):
        """Stop the token monitoring service"""
        if self.cleanup_task:
            self.cleanup_task.cancel()
        if self.session:
            await self.session.close()
        logger.info("Advanced OAuth token monitoring stopped")
    
    async def _monitor_tokens(self):
        """Main monitoring loop for token management"""
        while True:
            try:
                await self._refresh_expiring_tokens()
                await self._validate_random_tokens()
                await self._cleanup_invalid_tokens()
                await self._generate_analytics()
                
                await asyncio.sleep(self.refresh_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in token monitoring: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes on error
    
    async def _refresh_expiring_tokens(self):
        """Automatically refresh tokens that expire within 24 hours"""
        try:
            # Get all users with tokens expiring soon
            expiring_users = await self._get_expiring_tokens()
            
            refreshed_count = 0
            failed_count = 0
            
            for user_id, token_data in expiring_users:
                try:
                    if token_data['refresh_token']:
                        new_tokens = await self._refresh_user_token(token_data['refresh_token'])
                        if new_tokens:
                            await db_manager.update_user_oauth_tokens(
                                user_id, 
                                access_token=new_tokens['access_token'],
                                refresh_token=new_tokens.get('refresh_token'),
                                expires_in=new_tokens.get('expires_in', 604800)
                            )
                            refreshed_count += 1
                            logger.debug(f"Refreshed token for user {user_id}")
                        else:
                            failed_count += 1
                    else:
                        # No refresh token available, mark for re-auth
                        await self._schedule_reauth_notification(user_id)
                        
                except Exception as e:
                    logger.error(f"Failed to refresh token for user {user_id}: {e}")
                    failed_count += 1
            
            if refreshed_count > 0 or failed_count > 0:
                logger.info(f"Token refresh completed: {refreshed_count} refreshed, {failed_count} failed")
                
        except Exception as e:
            logger.error(f"Error in token refresh process: {e}")
    
    async def _get_expiring_tokens(self) -> List[Tuple[int, Dict]]:
        """Get tokens that expire within 24 hours"""
        try:
            # Calculate expiry threshold (24 hours from now)
            threshold = datetime.now() + timedelta(hours=24)
            
            all_users = await db_manager.get_authorized_users()
            expiring_tokens = []
            
            for user_id, username in all_users:
                token_data = await db_manager.get_user_oauth_tokens(user_id)
                if token_data['access_token'] and token_data['token_expires_at']:
                    try:
                        expires_at = datetime.fromisoformat(token_data['token_expires_at'])
                        if expires_at <= threshold:
                            expiring_tokens.append((user_id, token_data))
                    except ValueError:
                        # Invalid date format, treat as expired
                        expiring_tokens.append((user_id, token_data))
            
            return expiring_tokens
            
        except Exception as e:
            logger.error(f"Error getting expiring tokens: {e}")
            return []
    
    async def _refresh_user_token(self, refresh_token: str) -> Optional[Dict]:
        """Refresh a user's OAuth token using their refresh token"""
        try:
            data = {
                'client_id': AuthorizationConfig.CLIENT_ID,
                'client_secret': AuthorizationConfig.CLIENT_SECRET,
                'grant_type': 'refresh_token',
                'refresh_token': refresh_token
            }
            
            headers = {'Content-Type': 'application/x-www-form-urlencoded'}
            
            async with self.session.post(
                'https://discord.com/api/oauth2/token',
                data=data,
                headers=headers
            ) as response:
                if response.status == 200:
                    token_data = await response.json()
                    return token_data
                else:
                    logger.warning(f"Token refresh failed with status {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error refreshing token: {e}")
            return None
    
    async def _validate_random_tokens(self):
        """Randomly validate a sample of tokens to ensure they're still valid"""
        try:
            all_users = await db_manager.get_authorized_users()
            
            # Sample 10% of users or max 50 users for validation
            sample_size = min(max(1, len(all_users) // 10), 50)
            if len(all_users) > sample_size:
                import random
                sampled_users = random.sample(all_users, sample_size)
            else:
                sampled_users = all_users
            
            valid_count = 0
            invalid_count = 0
            
            for user_id, username in sampled_users:
                try:
                    is_valid = await AuthorizationConfig.test_oauth_token_validity(user_id)
                    await db_manager.mark_token_test(user_id, is_valid)
                    
                    if is_valid:
                        valid_count += 1
                    else:
                        invalid_count += 1
                        
                except Exception as e:
                    logger.error(f"Error validating token for user {user_id}: {e}")
                    invalid_count += 1
            
            if valid_count > 0 or invalid_count > 0:
                logger.debug(f"Token validation completed: {valid_count} valid, {invalid_count} invalid")
                
        except Exception as e:
            logger.error(f"Error in token validation: {e}")
    
    async def _cleanup_invalid_tokens(self):
        """Clean up tokens that have been invalid for more than 7 days"""
        try:
            # This would require additional database schema to track invalid tokens
            # For now, we'll just log the intent
            logger.debug("Token cleanup check completed")
            
        except Exception as e:
            logger.error(f"Error in token cleanup: {e}")
    
    async def _schedule_reauth_notification(self, user_id: int):
        """Schedule a notification for users who need to re-authorize"""
        try:
            # This would integrate with a notification system
            # For now, we'll log the need for re-auth
            logger.info(f"User {user_id} needs to re-authorize - no refresh token available")
            
        except Exception as e:
            logger.error(f"Error scheduling reauth notification: {e}")
    
    async def _generate_analytics(self):
        """Generate OAuth token analytics and health metrics"""
        try:
            all_users = await db_manager.get_authorized_users()

            # Handle case where no users are found
            if not all_users:
                logger.debug("No authorized users found for analytics generation")
                return

            total_users = len(all_users)
            
            # Count users with valid tokens
            valid_tokens = 0
            expired_tokens = 0
            missing_tokens = 0
            
            current_time = datetime.now()
            
            for user_id, username in all_users:
                token_data = await db_manager.get_user_oauth_tokens(user_id)
                
                if not token_data['access_token']:
                    missing_tokens += 1
                elif token_data['token_expires_at']:
                    try:
                        expires_at = datetime.fromisoformat(token_data['token_expires_at'])
                        if expires_at > current_time:
                            valid_tokens += 1
                        else:
                            expired_tokens += 1
                    except ValueError:
                        expired_tokens += 1
                else:
                    # No expiry date, assume valid
                    valid_tokens += 1
            
            # Log analytics periodically
            if hasattr(self, '_last_analytics_log'):
                time_since_last = current_time - self._last_analytics_log
                if time_since_last.total_seconds() < 3600:  # Only log once per hour
                    return
            
            self._last_analytics_log = current_time

            # Calculate percentage safely to avoid division by zero
            if total_users > 0:
                valid_percentage = (valid_tokens / total_users * 100)
                logger.info(
                    f"OAuth Token Analytics: {total_users} total users, "
                    f"{valid_tokens} valid tokens ({valid_percentage:.1f}%), "
                    f"{expired_tokens} expired tokens, "
                    f"{missing_tokens} missing tokens"
                )
            else:
                logger.info(
                    f"OAuth Token Analytics: No authorized users found. "
                    f"Analytics will be available once users complete authorization."
                )
            
        except Exception as e:
            logger.error(f"Error generating token analytics: {e}")

# Global instance
token_manager = AdvancedTokenManager()

async def start_token_manager():
    """Start the advanced token manager"""
    await token_manager.start_monitoring()

async def stop_token_manager():
    """Stop the advanced token manager"""
    await token_manager.stop_monitoring()