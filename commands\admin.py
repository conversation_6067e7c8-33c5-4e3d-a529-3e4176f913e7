"""
Admin commands with memory-efficient implementations.
Provides server configuration and management functionality.
"""
import discord
from discord.ext import commands
import asyncio
from typing import Optional

from core.logging import logger
from embeds.builders import create_success_embed, create_error_embed, create_info_embed, InfoEmbed
from interactions.views.base import ConfirmationView, PaginationView
from utils.database import db_manager
from config.messages import SystemConfig

def setup_admin_commands(bot: commands.Bot):
    """Setup admin commands"""
    
    @bot.command(name='setup')
    @commands.has_permissions(administrator=True)
    async def setup_server(ctx, 
                          welcome_channel: Optional[discord.TextChannel] = None,
                          activity_channel: Optional[discord.TextChannel] = None,
                          milestone_channel: Optional[discord.TextChannel] = None):
        """Configure server channels"""
        
        config_data = {}
        
        if welcome_channel:
            config_data['welcome_channel'] = welcome_channel.id
        if activity_channel:
            config_data['activity_channel'] = activity_channel.id
        if milestone_channel:
            config_data['milestone_channel'] = milestone_channel.id
        
        if not config_data:
            embed = create_error_embed(
                "No Configuration Provided",
                f"Usage: `{ctx.prefix}setup [welcome_channel] [activity_channel] [milestone_channel]`\n\n"
                f"Example: `{ctx.prefix}setup #welcome #general #milestones`"
            )
            await ctx.send(embed=embed)
            return
        
        success = await db_manager.update_guild_config(ctx.guild.id, **config_data)
        
        if success:
            embed = create_success_embed("✅ Configuration Updated")
            
            if welcome_channel:
                embed.add_field(name="Welcome Channel", value=welcome_channel.mention, inline=False)
            if activity_channel:
                embed.add_field(name="Activity Channel", value=activity_channel.mention, inline=False)
            if milestone_channel:
                embed.add_field(name="Milestone Channel", value=milestone_channel.mention, inline=False)
            
            await ctx.send(embed=embed.build())
            logger.info(f"Server configured by {ctx.author} in {ctx.guild}")
        else:
            embed = create_error_embed(
                "Configuration Failed",
                "There was an error saving the configuration. Please try again."
            )
            await ctx.send(embed=embed)
    
    @bot.command(name='config', aliases=['settings'])
    @commands.has_permissions(administrator=True)
    async def show_config(ctx):
        """Show current server configuration"""
        
        config = await db_manager.get_guild_config(ctx.guild.id)
        
        embed = InfoEmbed(
            f"⚙️ Server Configuration - {ctx.guild.name}"
        )
        
        if config:
            # Welcome channel
            welcome_ch = bot.get_channel(config['welcome_channel']) if config['welcome_channel'] else None
            embed.add_field(
                name="Welcome Channel",
                value=welcome_ch.mention if welcome_ch else "Not configured",
                inline=False
            )
            
            # Activity channel
            activity_ch = bot.get_channel(config['activity_channel']) if config['activity_channel'] else None
            embed.add_field(
                name="Activity Channel",
                value=activity_ch.mention if activity_ch else "Not configured",
                inline=False
            )
            
            # Milestone channel
            milestone_ch = bot.get_channel(config['milestone_channel']) if config['milestone_channel'] else None
            embed.add_field(
                name="Milestone Channel",
                value=milestone_ch.mention if milestone_ch else "Not configured",
                inline=False
            )
            
            # Settings
            embed.add_field(
                name="Activity Message Count",
                value=str(config['activity_message_count']),
                inline=True
            )
            embed.add_field(
                name="Milestone Count",
                value=str(config['milestone_count']),
                inline=True
            )
            embed.add_field(
                name="Last Member Count",
                value=str(config['last_member_count']),
                inline=True
            )
        else:
            embed.set_description("No configuration found. Use `!setup` to configure the server.")
        
        await ctx.send(embed=embed.build())
    
    @bot.command(name='auth_stats')
    @commands.has_permissions(administrator=True)
    async def auth_stats(ctx):
        """Show authorization statistics"""
        
        count = await db_manager.get_authorized_users_count()
        
        embed = InfoEmbed(
            "📊 Authorization Statistics",
            f"**Total Authorized Users:** {count}\n"
            f"**Server Members:** {ctx.guild.member_count}\n"
            f"**Authorization Rate:** {(count/ctx.guild.member_count*100):.1f}%"
        )
        
        await ctx.send(embed=embed.build())
        logger.debug(f"Auth stats viewed by {ctx.author}")
    
    @bot.command(name='auth_list')
    @commands.has_permissions(administrator=True)
    async def auth_list(ctx, page: int = 1):
        """Show list of authorized users"""
        
        users = await db_manager.get_authorized_users()
        
        if not users:
            embed = create_info_embed(
                "No Authorized Users",
                "No users have been authorized yet."
            )
            await ctx.send(embed=embed)
            return
        
        # Create pages (10 users per page)
        per_page = 10
        pages = []
        
        for i in range(0, len(users), per_page):
            page_users = users[i:i + per_page]
            
            embed = InfoEmbed(
                f"📋 Authorized Users (Page {i//per_page + 1})"
            )
            
            user_list = []
            for user_id, username in page_users:
                user_list.append(f"• {username} (`{user_id}`)")
            
            embed.set_description("\n".join(user_list))
            embed.set_footer(f"Total: {len(users)} users")
            
            pages.append(embed.build())
        
        if len(pages) == 1:
            await ctx.send(embed=pages[0])
        else:
            view = PaginationView(pages, user_id=ctx.author.id)
            message = await ctx.send(embed=pages[0], view=view)
            view.attach_message(message)
            
            # Register view for cleanup
            bot.register_view(f"auth_list_{message.id}", view)
    
    @bot.command(name='invite_users')
    @commands.has_permissions(administrator=True)
    async def invite_authorized_users(ctx, server_id: str, amount: int = 10):
        """Add authorized members directly to a server"""

        if amount > SystemConfig.MAX_INVITE_USERS_PER_COMMAND:
            embed = create_error_embed(
                "Too Many Users",
                f"Maximum {SystemConfig.MAX_INVITE_USERS_PER_COMMAND} users per command to prevent rate limits."
            )
            await ctx.send(embed=embed)
            return

        # Validate server ID
        try:
            target_guild = bot.get_guild(int(server_id))
            if not target_guild:
                embed = create_error_embed(
                    "Server Not Found",
                    "Server not found. Make sure the bot is in the target server."
                )
                await ctx.send(embed=embed)
                return
        except ValueError:
            embed = create_error_embed(
                "Invalid Server ID",
                "Please provide a valid server ID."
            )
            await ctx.send(embed=embed)
            return

        # Confirmation
        confirm_embed = InfoEmbed(
            "⚠️ Confirm Community Addition",
            f"This will add **{amount}** authorized members to **{target_guild.name}**.\n"
            f"**Target Server:** {target_guild.name} (`{server_id}`)\n\n"
            f"Are you sure you want to proceed?"
        ).build()
        
        view = ConfirmationView(user_id=ctx.author.id)
        message = await ctx.send(embed=confirm_embed, view=view)
        view.attach_message(message)
        
        # Wait for confirmation
        result = await view.wait_for_response()
        
        if result is True:
            # Get authorized users
            users = await db_manager.get_authorized_users(amount)
            invited = 0
            failed = 0
            
            # Progress message
            progress_embed = InfoEmbed(
                "📤 Adding Members to Community...",
                f"Progress: 0/{len(users)}"
            ).build()

            progress_msg = await ctx.send(embed=progress_embed)

            for i, (user_id, username) in enumerate(users):
                try:
                    user = await bot.fetch_user(user_id)

                    # Try to add user directly to the server
                    try:
                        await target_guild.fetch_member(user_id)
                        # User already in server
                        continue
                    except discord.NotFound:
                        # User not in server, try to add them using OAuth token
                        from utils.oauth_server import oauth_server
                        oauth_token = None
                        
                        if oauth_server and user_id in oauth_server.pending_authorizations:
                            oauth_data = oauth_server.pending_authorizations[user_id]
                            oauth_token = oauth_data.get('access_token')
                        
                        if oauth_token:
                            try:
                                # Add user directly using OAuth token via Discord API
                                import aiohttp

                                # Discord API endpoint for adding guild member
                                url = f"https://discord.com/api/v10/guilds/{target_guild.id}/members/{user.id}"
                                headers = {
                                    "Authorization": f"Bot {bot.http.token}",
                                    "Content-Type": "application/json"
                                }
                                data = {
                                    "access_token": oauth_token
                                }

                                async with aiohttp.ClientSession() as session:
                                    async with session.put(url, headers=headers, json=data) as response:
                                        if response.status in [200, 201]:
                                            invited += 1

                                            # Send notification DM
                                            await user.send(
                                                f'**Added to {target_guild.name}**\n\n'
                                                f'You have been added to this Discord server.\n\n'
                                                f'**Server features you can now access:**\n'
                                                f'• Special events and giveaways\n'
                                                f'• Community discussions\n'
                                                f'• Member-only channels\n\n'
                                                f'Welcome to the community!'
                                            )
                                            logger.info(f"Successfully added {user} to {target_guild.name} using OAuth token")
                                        else:
                                            error_text = await response.text()
                                            logger.warning(f"Failed to add {user} using OAuth token - API error {response.status}: {error_text}")
                                            failed += 1

                            except Exception as e:
                                logger.warning(f"Failed to add {user} using OAuth token: {e}")
                                failed += 1
                        else:
                            # No OAuth token available
                            try:
                                await user.send(
                                    f'**Authorization Required**\n\n'
                                    f'To be added to {target_guild.name}, you need to authorize the bot first.\n\n'
                                    f'Please use the `/authorize` command in the main server, complete the authorization, '
                                    f'then use `/confirm_auth` to enable community access.'
                                )
                            except:
                                pass
                            failed += 1

                    # Update progress every 5 users
                    if (i + 1) % 5 == 0:
                        progress_embed = InfoEmbed(
                            "📤 Adding Members to Community...",
                            f"Progress: {i + 1}/{len(users)}\n"
                            f"Added: {invited} | Failed: {failed}"
                        ).build()
                        await progress_msg.edit(embed=progress_embed)

                    # Rate limit prevention
                    await asyncio.sleep(1)

                except Exception as e:
                    failed += 1
                    logger.debug(f"Failed to add user {username}: {e}")

            # Final result
            result_embed = create_success_embed(
                "✅ Community Access Granted",
                f"**Successfully added:** {invited}\n"
                f"**Failed:** {failed}\n"
                f"**Total processed:** {len(users)}"
            )
            
            await progress_msg.edit(embed=result_embed)
            logger.info(f"Invitations sent by {ctx.author}: {invited} successful, {failed} failed")
        
        elif result is False:
            cancelled_embed = create_info_embed(
                "❌ Cancelled",
                "Invitation process cancelled."
            )
            await message.edit(embed=cancelled_embed, view=None)
        
        else:
            timeout_embed = create_error_embed(
                "⏰ Timeout",
                "Confirmation timed out. No invitations were sent."
            )
            await message.edit(embed=timeout_embed, view=None)
    
    @bot.command(name='cleanup')
    @commands.has_permissions(administrator=True)
    async def cleanup_command(ctx, amount: int = 10):
        """Clean up bot messages"""
        
        if amount > SystemConfig.MAX_CLEANUP_MESSAGES:
            embed = create_error_embed(
                "Too Many Messages",
                f"Maximum {SystemConfig.MAX_CLEANUP_MESSAGES} messages can be cleaned at once."
            )
            await ctx.send(embed=embed)
            return
        
        try:
            deleted = 0
            async for message in ctx.channel.history(limit=amount * 2):  # Check more messages
                if message.author == bot.user:
                    await message.delete()
                    deleted += 1
                    if deleted >= amount:
                        break
                    await asyncio.sleep(0.5)  # Rate limit prevention
            
            embed = create_success_embed(
                "🧹 Cleanup Complete",
                f"Deleted {deleted} bot messages."
            )
            
            # Delete this message after 5 seconds
            cleanup_msg = await ctx.send(embed=embed)
            await asyncio.sleep(5)
            await cleanup_msg.delete()
            
            logger.info(f"Cleanup performed by {ctx.author}: {deleted} messages deleted")
            
        except discord.Forbidden:
            embed = create_error_embed(
                "Missing Permissions",
                "I don't have permission to delete messages in this channel."
            )
            await ctx.send(embed=embed)
        except Exception as e:
            logger.error(f"Error in cleanup command: {e}")
            embed = create_error_embed(
                "Cleanup Failed",
                "An error occurred during cleanup."
            )
            await ctx.send(embed=embed)
    
    logger.info("Admin commands loaded")
